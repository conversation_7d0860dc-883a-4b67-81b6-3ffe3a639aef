import React from 'react';
import { connect } from 'react-redux';
import TravelTidbitsContainer from '../Components/ItineraryV2/TravelTidbits/TravelTidbitsContainer';
import { showTravelTidbitsV2 } from '../../utils/HolidaysPokusUtils';
import TravelTidbitsContainerV2 from '../Components/ItineraryV2/TravelTidbitsV2/TravelTidbitsContainerV2';
const mapDispatchToProps = dispatch => ({});

const TravelTidBitsOverlayContainer = (props) => {
  return showTravelTidbitsV2() ? <TravelTidbitsContainerV2 {...props}/> : <TravelTidbitsContainer {...props}/>;
}
const mapStateToProps = (state) => {
  return {...state.holidaysActivityOverlay, metadataDetail: state.holidaysDetail.detailData.packageDetail.metadataDetail }
  };
export default connect(mapStateToProps, mapDispatchToProps)(TravelTidBitsOverlayContainer);
