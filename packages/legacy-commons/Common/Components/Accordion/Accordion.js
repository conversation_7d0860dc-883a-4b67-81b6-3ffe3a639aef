import React from 'react';
import PropTypes from 'prop-types';
import {Animated, View, Image, TouchableWithoutFeedback, StyleSheet} from 'react-native';
import TouchableOpacityCommon from '../TouchableOpacity';

const arrowDown = require('@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp');
const arrowUp = require('@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp');

export default class Accordion extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      collapsed: props.defaultCollapsed,
      animatedHeight: new Animated.Value(0)
    };
  }

  toggleAccordion = () => {
    const {accordionOpenCallback, accordingCloseCallback} = this.props;
    if (this.state.collapsed) {
      Animated.timing(
        this.state.animatedHeight,
        {
          toValue: 440,
          duration: 500
        }
      ).start();
    } else {
      Animated.timing(
        this.state.animatedHeight,
        {
          toValue: 0,
          duration: 500
        }
      ).start();
    }

    this.setState({
      collapsed: !this.state.collapsed
    }, () => {
      if (this.state.collapsed) {
        accordingCloseCallback();
      } else {
        accordionOpenCallback();
      }
    });
  }

  render() {
    const {renderHeader, renderContent, headerStyle} = this.props;
    const {collapsed} = this.state;
    return (
      <View>
        <TouchableOpacityCommon onPress={this.toggleAccordion} testID={this.props?.testID}>
          <View style={[styles.headerContainer, headerStyle]}>
            {renderHeader()}
              <Image
                source={collapsed ? arrowDown : arrowUp}
                style={{width: 24, height: 24, marginTop: 5}}
              />
          </View>
          </TouchableOpacityCommon>
        {!collapsed &&
          <Animated.View>
            {renderContent()}
          </Animated.View>
        }
      </View>
    );
  }
}

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',  justifyContent: 'space-between', paddingRight: 16
  }
});

Accordion.propTypes = {
  defaultCollapsed: PropTypes.bool,
  renderHeader: PropTypes.func.isRequired,
  renderContent: PropTypes.func.isRequired,
  headerStyle: PropTypes.object,
  accordionOpenCallback: PropTypes.func,
  accordingCloseCallback: PropTypes.func
};

Accordion.defaultProps = {
  defaultCollapsed: false,
  headerStyle: {},
  accordionOpenCallback: () => {},
  accordingCloseCallback: () => {}
};
