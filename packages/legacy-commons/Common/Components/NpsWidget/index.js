import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Actions } from 'react-native-router-flux';
import _ from 'lodash';
import PropTypes from 'prop-types';
import TouchableRipple from '../TouchableRipple';
import { fonts, colors } from '../../../Styles/globalStyles';
import Card from '../Card';

const ratingColors = [
  ..._.times(7).map((_) => '#f15c5a'),
  ..._.times(2).map((_) => '#3f8ed2'),
  ..._.times(2).map((_) => '#5b9e06'),
];

const NpsRatingIndicator = ({ text, id, onClick }) => (
  <View
    style={{
      flex: 1,
      margin: 1,
      backgroundColor: ratingColors[id],
    }}
  >
    <TouchableRipple onPress={() => onClick(id)}>
      <Text
        style={{
          fontFamily: fonts.bold,
          fontSize: 14,
          padding: 2,
          lineHeight: 30,
          color: colors.white,
          textAlign: 'center',
        }}
      >
        {text}
      </Text>
    </TouchableRipple>
  </View>
);

NpsRatingIndicator.propTypes = {
  id: PropTypes.number.isRequired,
  onClick: PropTypes.func.isRequired,
  text: PropTypes.string.isRequired
};

class NpsWidget extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      alreadyShownNps: true,
    };
  }
  async componentDidMount() {
    const alreadyShownNps = await AsyncStorage.getItem(this._getNpsShowKey());
    // eslint-disable-next-line react/no-did-mount-set-state
    this.setState({
      alreadyShownNps: !!alreadyShownNps,
    });
  }

  _getNpsShowKey = () => `nps_card_shown_${this.props.bookingId}`;

  _openWebView = (id) => {
    const { lob, bookingId } = this.props;
    const category = `postsales_${lob.toLowerCase()}`;
    Actions.openWebView({
      headerText: 'Review your booking',
      headerIcon: require('@mmt/legacy-assets/src/back-white.webp'),
      url: `https://customer-experience.makemytrip.com/nps/nps.html?bookingID=${bookingId}&LOB=${lob}&category=${category}&npsRating=${id}`,
    });
    AsyncStorage.setItem(this._getNpsShowKey(), 'true');
    this.setState({
      alreadyShownNps: true,
    });
  };

  render() {
    if (this.state.alreadyShownNps) {
      return null;
    }
    return (
      <Card style={{ marginHorizontal: 0, marginBottom: 12 }}>
        <View style={styles.container}>
          <Text style={styles.headerText}>
            Considering your overall experience, how likely would you be to recommend MakeMyTrip?
          </Text>
          <View style={styles.indicatorBar}>
            {_.times(11).map((val, index) => (
              <NpsRatingIndicator
                key={val}
                text={`${index}`}
                id={index}
                onClick={this._openWebView}
              />
            ))}
          </View>
          <View style={styles.indicatorLabelContainer}>
            <Text
              style={{
                fontFamily: fonts.regular,
                fontSize: 12,
                color: colors.defaultTextColor,
              }}
            >
              0 - Not Likely
            </Text>
            <Text
              style={{
                fontFamily: fonts.regular,
                fontSize: 12,
                color: colors.defaultTextColor,
              }}
            >
              10 - Most Likely
            </Text>
          </View>
        </View>
      </Card>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  headerText: {
    color: '#000000',
    fontSize: 16,
    fontFamily: fonts.bold,
  },
  indicatorBar: {
    flexDirection: 'row',
    marginTop: 12,
  },
  indicatorItem: {},
  indicatorLabelContainer: {
    marginTop: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

NpsWidget.propTypes = {
  bookingId: PropTypes.string.isRequired,
  lob: PropTypes.string.isRequired
};

export default NpsWidget;
