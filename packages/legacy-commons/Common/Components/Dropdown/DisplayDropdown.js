/* eslint-disable object-curly-spacing */
import React from 'react';
import PropTypes from 'prop-types';
import { View, Text, StyleSheet } from 'react-native';

import { colors, fonts } from '../../../Styles/globalStyles';

import Label from '../Label/label';
import TouchableRipple from '../TouchableRipple';


export default function DisplayDropdown(props) {
  const { label, selectedValue, nationality_indian } = props;
  return (
    <View>
      <Label label={label} />
      <TouchableRipple>
        <View style={styles.dropdownContainer}>
            <Text style={styles.displayText}>{nationality_indian||'Indian'}</Text>
        </View>
      </TouchableRipple>
    </View>
  );
}

const styles = StyleSheet.create({
  dropdownContainer: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.purpleyGrey,
    height: 48,
    paddingLeft: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  displayText: {
    color: colors.textGrey,
    fontSize: 14,
    fontFamily: fonts.bold
  }
});

DisplayDropdown.propTypes = {

};
