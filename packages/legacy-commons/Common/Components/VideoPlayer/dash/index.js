import React from 'react';
import {View, Dimensions} from 'react-native';
import ReactPlayer from 'react-native-video';
import styles from './webPlayerCss';


const WebDashVideoPlayer = ({source, autoPlay, width, height, muted, repeat}) => (
  <View style={{...styles.container, width : (width ? width : Dimensions.get('window').width), height :  (height ? height :  170) } }>
    <ReactPlayer url={source.uri} playing={autoPlay} muted={muted} repeat={repeat} controls width={width ?? Dimensions.get('window').width} height={height ?? 170}   resizeMode={"stretch"} 
    />
  </View>
);

export default WebDashVideoPlayer;
