import React from 'react';
import {
  Text,
  TouchableOpacity,
  Image,
  Animated,
  Easing,
  StyleSheet
} from 'react-native';
import PropTypes from 'prop-types';
import HTMLView from 'react-native-htmlview';
import {Actions} from 'react-native-router-flux';

import {colors, fonts} from '../../../Styles/globalStyles';
import checkedIcon from '@mmt/legacy-assets/src/tnc_check_active.webp';
import unCheckedIcon from '@mmt/legacy-assets/src/tnc_check_inactive.webp';

import {trackOmniture} from '../../utils/OmnitureTrackerUtils';
import whatsappIcon from '@mmt/legacy-assets/src/ic_whatsapp.webp';
import {PROP_30} from '../../utils/OmnitureTrackerUtils';

export default class TncView extends React.Component {
  constructor(props) {
    super(props);
    this.shakeAnimations = new Animated.Value(0);
    this.state = {
      isTncChecked: this.props.isTncChecked
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.shake) {
      this.showShakeAnimation();
    }
  }

  componentDidMount() {
    if (this.props.shake) {
      this.showShakeAnimation();
    }
  }

  showShakeAnimation = () => {
    this.shakeAnimations.setValue(0);
    Animated.timing(this.shakeAnimations, {
      toValue: 1,
      duration: 300,
      easing: Easing.linear
    })
      .start();
  };


  trackClickEvent = (pageName, clickEvent) => {
    const dict = {};
    dict.m_c50 = clickEvent;
    dict.pageName = pageName;
    dict.m_v15 = pageName;
    trackOmniture(pageName, dict);
  }

  handleTncChecked = () => {
    const newState = !this.state.isTncChecked;
    this.setState({isTncChecked: newState});
    this.props.onTncCheckedChange(newState);
  };

  onLinkPress = (url) => {
    this.props.onTncClick();
    Actions.commonWebView({
      url,
      onTncClose: this.onTncClose,
      headerText: 'Terms and Conditions'
    });
    if (this.props.pageName && this.props.pageName === 'infoCard') {
      this.trackClickEvent(PROP_30, 'Pt_post_top_tnc_clicked');
    }
  };



  onTncClose = () => {
    this.props.onTncClose();
  };

  render() {
    const marginLeft = this.shakeAnimations.interpolate({
      inputRange: [0, 0.2, 0.4, 0.6, 0.8, 0.9, 1],
      outputRange: [0, -10, 10, -10, 10, -10, 0]
    });
    const {showCheckBox, showWhatsappIcon, html} = this.props;
    return (
      <Animated.View style={[styles.container, {marginLeft}, this.props.style]}>
        {showCheckBox &&
          <TouchableOpacity
            onPress={this.handleTncChecked}
          >
            <Image
              style={styles.icon}
              source={this.state.isTncChecked ? checkedIcon : unCheckedIcon}
            />
          </TouchableOpacity>
        }
        <HTMLView
          style={{marginHorizontal: 5, backgroundColor: 'transparent'}}
          value={`<q><p>${html}</p></q>`}
          stylesheet={htmlStyle}
          onLinkPress={this.onLinkPress}
          renderNode={this.renderNode}
        />
        {showWhatsappIcon &&
          <Image
            style={styles.whatsappIcon}
            source={whatsappIcon}
          />
        }
      </Animated.View>
    );
  }

  renderNode = (node, index, siblings, parent, defaultRenderer) => {
    if (node.name === 'font') {
      const a = node.attribs;
      return (
        <Text key={index} style={[htmlStyle.p, a.color ? {color: a.color} : {}]}>
          {node.children.map(item => item.data)}
        </Text>
      );
    }
    return undefined;
  }
}





TncView.propTypes = {
  html: PropTypes.string.isRequired,
  showCheckBox: PropTypes.bool,
  isTncChecked: PropTypes.bool,
  shake: PropTypes.bool.isRequired,
  onTncCheckedChange: PropTypes.func.isRequired,
  showWhatsappIcon: PropTypes.bool,
  style: PropTypes.object,
  pageName: PropTypes.string,
  onTncClick: PropTypes.func,
  onTncClose: PropTypes.func
};
TncView.defaultProps = {
  showCheckBox: true,
  isTncChecked: true,
  showWhatsappIcon: false,
  style: {},
  onTncClick: () => {},
  onTncClose: () => {}
};

const htmlStyle = StyleSheet.create({
  q: { /* fix fo Html Fonts Not working without adding blank tag */

  },
  p: {
    color: colors.white,
    fontSize: 12,
    fontFamily: fonts.regular
  },
  b: {
    color: colors.white,
    fontSize: 12,
    fontFamily: fonts.black
  },
  a: {
    textDecorationLine: 'underline'
  }
});
const styles = {
  container: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: 'white'
  },
  whatsappIcon: {
    width: 16,
    height: 16
  }
};
