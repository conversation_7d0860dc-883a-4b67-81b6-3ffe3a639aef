import React, {Component} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import PropTypes from 'prop-types';

class Counter extends Component {
  constructor(props) {
    super(props);
    this.min = props.min || 0;
    this.max = props.max || 5;


  }

  increment = () => {

    if (this.isMaxDisable()) return;

    this.props.handleCountChange('INC');
  };

  decrement = () => {

    if (this.isMinDisable()) return;
    this.props.handleCountChange('DEC');
  };

  pad = (n) => n < 10 ? ('0' + n) : n;
  isMinDisable = () => this.props.value <= this.min;
  isMaxDisable = () => this.props.value >= this.max;

  render() {
    const activeBtnOpacity = 0.7;
    const plusStyle = [styles.btnText];
    const minusStyle = [styles.btnText];
    const containerStyle = [styles.button];
    const {value} = this.props;


    return (
      <View style={styles.container}>
        <TouchableOpacity style={containerStyle} onPress={this.decrement}
                          activeOpacity={activeBtnOpacity}>
          <Text style={minusStyle}>-</Text>
        </TouchableOpacity>
        <Text style={styles.value}>{this.pad(value)}</Text>
        <TouchableOpacity style={containerStyle} onPress={this.increment}
                          activeOpacity={activeBtnOpacity}>
          <Text style={plusStyle}>+</Text>
        </TouchableOpacity>
      </View>
    );
  }
}

Counter.propTypes = {
  handleCountChange: PropTypes.func.isRequired,
  value: PropTypes.number.isRequired
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 4,
    borderStyle: 'solid',
    borderWidth: 0.9,
    borderColor: '#979797',
    paddingVertical: 8,
    paddingHorizontal: 15
  },
  value: {
    marginHorizontal: 20,
    fontFamily: 'Lato-Bold',
    fontSize: 22,
    fontWeight: 'bold',
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'center',
    color: '#040404',
    minWidth: 28
  },
  btnText: {
    color: '#4a4a4a',
    fontFamily: 'Lato-Bold',
    fontSize: 20,
    fontStyle: 'normal',
    fontWeight: 'bold',
    letterSpacing: 0,
    textAlign: 'center'
  },
  disableBtnText: {
    opacity: 0.3
  }
});

export default Counter;

