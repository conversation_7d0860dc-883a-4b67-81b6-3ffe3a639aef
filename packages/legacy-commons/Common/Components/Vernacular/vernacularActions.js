export const ACTION_SET_VERNACULAR_CONFIG = '@common/ACTION_SET_VERNACULAR_CONFIG';
export const ACTION_FETCH_LANGUAGE = '@common/FETCH_LANGUAGE';
export const FETCH_LANGUAGE_DONE = '@common/FETCH_LANGUAGE_DONE';



export const updateLanguageStatus = (status, lang) => dispatch => dispatch({ type: ACTION_SET_VERNACULAR_CONFIG, payload: { status, lang } });
export const onLanguageFetchComplete = (lang) => dispatch => dispatch({ type: FETCH_LANGUAGE_DONE, payload: { lang } });

