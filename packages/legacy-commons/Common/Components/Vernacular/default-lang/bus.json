{"bus": "Bus", "buses": "buses", "seat": "<PERSON><PERSON>", "seats": "Seats", "apply": "APPLY", "refresh": "REFRESH", "book_a_train": "BOOK A TRAIN", "discounts": "Discounts", "discount": "discount", "adults": "Adults", "adult": "Adult", "child": "Child", "children": "Children", "close": "CLOSE", "info": "INFO", "remove": "REMOVE", "expired": "Expired", "code": "CODE", "tap_to_copy": "Tap to copy", "view_all": "View All", "cancel": "Cancel", "settings": "Settings", "landing.travelling_from": "TRAVELLING FROM", "from": "FROM", "landing.travelling_to": "TRAVELLING TO", "to": "TO", "gps": "GPS", "route": "Route", "skip": "SKIP", "submit": "submit", "offers_title": "OFFERS", "okay": "Okay", "offers_tap_to_view": "*Tap to View T&Cs", "coupon_is_applied_successfully": "Coupon is applied successfully.", "coupon_cannot_be_applied": "Coupon cannot be applied.", "coupon_codes": "Coupon Codes", "loading_best_offer": "Loading best offers for you!", "enter_city_name": "Enter City Name", "enter_source_city_name": "Enter Source City Name", "enter_destination_city_name": "Enter Destination City Name", "search_error_label": "Oops! We didn’t get you.", "re_enter": "Try entering a different building name/area or city name or check the spelling of location entered", "popular_locations": "POPULAR LOCATIONS", "popular_locations_near_you": "POPULAR LOCATIONS NEAR YOU", "popular_top_cities": "POPULAR TOP CITIES", "popular_cities": "POPULAR CITIES", "smart_search_label": "Search smarter now!! Now search for your bus stop directly", "search_suggestion": "Try searching for <PERSON><PERSON><PERSON><PERSON> instead of Bengaluru", "board_at": "Board At", "drop_at": "Drop At", "current_location": "Use Current Location", "using_gps": "USING GPS", "permission": "Location Permission", "request": " Allow MakeMyTrip access to your location?", "request_description": "Access to your exact location helps us locate bus stops near you accurately.", "not_now": "NOT NOW", "go_ahead": "GO AHEAD", "fetching": "Fetching your location", "no_permission": "Location permission is permanently disabled, Please go to app settings and allow location.", "login_prompt": "<p>Login now to <s> avail exciting deals and discounts </s> specially crafted for you </p>", "login_cta": "LOGIN", "login_now": "LOGIN NOW to get the best deals!!", "lmd": "Last Minute Deal", "tod": "Travel Operator Deal", "cd": "Coupon Discount", "last": "Last", "with": "with", "off": "off", "unlock": "UNLOCK", "few": "few", "in_transit": "IN TRANSIT", "ratings": "ratings", "no_ratings": "No Ratings", "next_day": "Next day", "listing.my_deals_login_toast": "Yay! You saved extra**₹{{ld}}**on MyDeals", "listing.login_now": "Login Now", "listing.plus_one_day": "+1 day", "seats_left": "Seats Left", "your_gift_voucher": "Your Gift Voucher", "view_t&cs": "View T&Cs", "use_coupon_code": "Use coupon code", "pre-applied": "PRE-APPLIED", "my": "My", "safety": "Safety", "loginNow": "Login Now", "to_explore": " to explore", "more_offers": "more offers", "save": "Save", "bus_tickets": "bus tickets", "wifi": "Wifi", "newspaper": "Newspaper", "headsets": "Headsets", "water_bottle": "Water bottle", "blankets": "blankets", "Snacks": "Snacks", "Charging_point": "chargingPointIcon", "movie": "Movie", "reading_light": "Reading light", "pillow": "Pillow", "bathroom": "Bathroom", "personal_tv": "Personal Tv", "heater": "Heater", "satellite": "Satellite", "food": "Food", "track_my_bus": "Track my bus", "toilet_paper": "Toilet paper", "soft_drink": "Soft drink", "safety_belt": "Safety belt", "emergency_exit": "Emergency exit", "fire_extinguisher": "Fire extinguisher", "hammer": "Hammer (to break glass)", "bus_hostess": "Bus hostess", "cctv": "Cctv", "emergency_contact_number": "Emergency contact number", "cup_holder": "Cup holder", "music": "Music/mp3", "reclining_seat": "Reclining seat", "on_time_guarantee": "On-time guarantee", "e_ticket": "E-ticket", "smoking_room": "Smoking room", "toilet": "<PERSON><PERSON><PERSON>", "facial_tissues": "Facial tissues", "tour_guide": "Tour guide", "massage_chair": "Massage chair", "wet_napkin": "Wet napkin", "air_freshner": "Air freshner", "meal": "<PERSON><PERSON>", "air_conditioner": "Air conditioner", "disposal_seat_covers": "Disposal seat covers", "wheel_chair_assist": "Wheel chair assist", "central_tv": "Central tv", "call_center_support": "24/7 call center support", "hand_sanitisers_provided": "Hand sanitisers provided", "staff_with_masks": "Staff with masks", "no_blankets_linens": "No blankets/linens", "deep_cleaned_buses": "Deep cleaned buses", "regular_temperature_checks": "Regular temperature checks", "busFromTo": "{{from}} to {{to}}", "busToFor": "{{fromCity}} to {{toCity}} bus for {{date}}", "available": "Available", "female": "Female", "male": "Male", "unisex": "Unisex", "selected": "Selected", "booked": "Booked", "blocked": "Blocked", "type_in_english": "Type in English", "terms_and_conditions": "Terms and Conditions", "offer_details": "Offer Details", "more": "MORE", "fetching_your_location": "Fetching your location", "done": "DONE", "clear": "CLEAR", "all": "All", "flipkart_reward": "Click here to view your Flipkart Plus rewards", "males": "males", "females": "females", "blue": "blue", "pink": "pink", "single_seats": "Single Seats", "proceed": "PROCEED", "pickup": "Pickup", "drop": "Drop", "charges": "Charges", "operators": "OPERATORS", "learn_more": "Learn More", "onwards": "ONWARDS", "select_pax_seats": "Select {{paxCnt}} Seats", "select_date": "Select Date", "clear_all": "CLEAR ALL", "explore_all_buses": "EXPLORE ALL BUSES", "retry": "retry", "choose_dates": "Choose dates", "toast.date_auto_adjusted_msg": "Your date has been auto adjusted to today, {{date}}", "coupon_copied_to_clipboard": "Coupon Copied to Clipboard", "landing.bus": "Bus", "landing.bus_ticket_booking": "Bus Ticket Booking", "landing.search": "Search", "landing.bus_hire": "BUS HIRE", "landing.search_bus": "SEARCH BUSES", "landing.date": "DATE", "landing.today": "Today", "landing.tomorrow": "Tomorrow", "something_went_wrong": "Something went wrong", "landing.recent_searches": "RECENT SEARCHES", "landing.enter_city_or_bus_stop": "Enter City or Bus Stop", "listing.busFromTo": "{{from}} to {{to}}", "listing.seatsLeft": "{{seatsLeft}} Seats Left", "listing.ratings": "{{numberOfRatings}} ratings", "listing.no_rating": "No Rating", "listing.banner_install_app": "INSTALL APP", "listing.banner_header_text": "Download App & Save More ", "listing.banner_sub_text": "Use coupon code WELCOMEMMT on App", "listing.red_deal_saving_label": "Additional savings upto ₹{{firstUserRedDealPrice}} on login", "listing.pickup_drop": "Pickup/Drop", "listing.hide_all_buses": "HIDE ALL BUSES", "listing.view_all_buses": "VIEW ALL BUSES", "listing.hide_buses": "HIDE BUSES", "listing.view_buses": "VIEW BUSES", "listing.sort_and_filter": "Sort & Filter", "listing.bus_edit_wigdet.btn_title": "MODIFY SEARCH", "listing.bus_edit_widget.heading_text": "Bus Search", "listing.buses_found": "buses found", "listing.buses_count_found": "{{busCount}} Buses found", "listing.clear_all": "CLEAR ALL", "listing.sort_by": "SORT BY", "listing.filters": "FILTERS", "listing.view_primo_buses_only": "Filter and view only the", "listing.end_of_results": "End of Results", "listing.bps_left": "{{bpsLeft}} others", "listing.bus_started": "BUS STARTED to {{topBPs}}", "listing.show_bus_results": "SHOW {{busCount}} RESULTS", "listing.earliest": "EARLIEST", "listing.cheapest": "CHEAPEST", "listing.fastest": "FASTEST", "filter.bus_stops": "Bus Stops", "filter.search_drop_points": "Search Drop Points", "filter.search_pickup_points": "Search Pickup points", "filter.travel_operators": "Travel Operators", "filter.search_travel_operator": "Search Travel Operators", "filter.tooltip_content": "Introducing filters for seat prices", "filter.too_many_filters": "Too many filters applied! We couldn’t find any bus", "filter.more_filter": "{{numberOfOptions}} MORE {{type}}", "seatmap.review.male_only": "(Male only)", "seatmap.review.female_only": "(Female only)", "seatmap.review.header.title": "Review Booking", "seatmap.review.insurance_persuation": "Over {{persuation_value}}% of our bus travelers choose to protect their journey", "seatmap.header.title": "Select Seats", "seatmap.review_booking_heading": "Review Booking", "seatmap.pickup_drop_heading": "Select Pickup & Drop Points", "seatmap.seat_gender.male": "MALE", "seatmap.seat_gender.female": "FEMALE", "seatmap.seat_gender.unspecified": "NA", "seatmap.berth": "{{deckType}} BERTH", "seatmap.berthWithCount": "{{deckType}} BERTH ({{availableSeatCount}})", "seatmap.seatPrice": "<PERSON><PERSON>", "seatmap.legend_info": "INFO", "seatmap.tab.reviews": "REVIEWS", "seatmap.tab.photos": "PHOTOS", "seatmap.tab.amenities": "AMENITIES", "seatmap.tab.rest_stops": "REST STOPS", "seatmap.tab.policies": "POLICIES", "seatmap.single_gender_banner": "If you're a {{gender}} travelling alone, choose a {{_gender}} seat only", "seatmap.next": "Next", "seatmap.continue": "CONTINUE", "seatmap.offers": "Offers", "seatmap.fare_details": "Fare Details", "seatmap.offers_count": "{{offerCount}} Offers", "seatmap.offer_count": "{{offerCount}} Offer", "seatmap.seats_count_label": "For {{seatCount}} Seats", "seatmap.seat_count_label": "For {{seatCount}} Seat", "seatmap.pick_points": "PICKUP POINTS", "seatmap.drop_points": "DROP POINTS", "seatmap.selectedSeats": "Selected Seats : ", "select_stop.header.title": "Select Pickup & Drop Points", "seatmap.traveller_heading": "Traveller Details", "seatmap.traveller_name": "Name", "seatmap.traveller_female_seat": "Female Seat", "seatmap.traveller_male_seat": "<PERSON> Seat", "seatmap.traveller.male": "Male", "seatmap.traveller.female": "Female", "seatmap.traveller_age": "Age", "seatmap.seat": "<PERSON><PERSON>", "seatmap.traveller_gender": "Gender", "seatmap.traveller_card_number": "Card Number", "seatmap.traveller_card_error_message": "Please enter a valid card number", "seatmap.traveller_govt_id_type": "Govt. ID Type", "seatmap.traveller_chosse_id_type": "Choose ID Type", "seatmap.traveller_govt_id_number": "Govt. ID Number", "seatmap.traveller_enter_govt_id_number": "Enter Govt. ID Number", "seatmap.traveller_id_error_message": "Both Govt ID Type and Number are required.", "seatmap.traveller.empty_name": "Name is empty", "seatmap.traveller.valid_name": "Name can contain only letters", "seatmap.traveller.empty_age": "Age is empty", "seatmap.traveller.valid_age": "Age should be a number", "seatmap.traveller.zero_age_validation": "Age cannot be 0", "seatmap.traveller.max_age_validation": "Age should be less than 120 years", "seatmap.traveller.empty_gender": "Please select the gender", "seatmap.traveller.invalid_gender": "Invalid gender", "seatmap.lower": "LOWER", "seatmap.upper": "UPPER", "seatmap.legendinfo": "Know your seats while selecting", "seatmap.legend_header": "Seater / Sleeper info", "seatmap.seat_map_default_error_msg": "Bus operator unreachable. Fetching bus list again!", "seatmap.select_droping_point": "Please select Dropping Point", "seatmap.select_apsrtc_concession": "Select Your APSRTC Concession", "seatmap.navya_cat_card": "Navya CAT Card", "seatmap.cat_card_number": "CAT Card number will be asked later", "seatmap.senior_citizen_concession": "Senior Citizen Concession(60 yrs or more)", "seatmap.govt_id_number": "Govt. ID number will be asked later", "seatmap.general_public": "General Public", "seatmap.mmt_offer_applicable": "MakeMyTrip offers are still applicable", "seatmap.single_lady": "Single Lady", "seatmap.single_male": "Single Male", "seatmap.bpdp_left": "The bus is in transit and has left some boarding points. Next up is..", "seatmap.fdc_text": "Free Date Change", "seatmap.select_seats": "Please select Seats", "seatmap.select_boarding_point": "Please Select Boarding Point", "contactinfo.email_address": "Email address", "contactinfo.optional": "Optional", "contactinfo.email_placeholder": "Email ID", "contactinfo.email_address_placeholder": "EMAIL ADDRESS", "contactinfo.details_sub_header": "We’ll send your ticket here", "contactinfo.email_error_message": "Please enter a valid email", "contactinfo.phone_number": "Phone number", "contactinfo.mobile_number": "MOBILE NO.", "contactinfo.error_message": "Please enter a valid {{error_type}}", "contactinfo.contact_details_heading": "Enter Contact Details to receive your e-ticket and updates", "contactinfo.contact_details": "Contact Details", "contactinfo.country_code": "country code", "favourite_trips": "Favourite Trips", "tap_on_heart1": "Tap on the heart", "tap_on_heart2": "to save your travel itinerary for a faster booking experience in the future.", "know_more": "Know More", "offers.title": "Offers & Discounts", "offers.rewards_bonus_title": "Reward Bonus Available!", "offers.reards_bonus_msg": "Use reward bonus on payment page for max savings!", "offers.wallet_bonus": "Wallet Bonus", "offers.wallet_reward_cant_club": "Wallet rewards can’t be clubbed with this coupon", "offers.mydeal_applied": "APPLIED", "offers.concession": "Concession", "offers.reward_bonus_applied": "Reward Bonus Applied", "offers.coupon_discount_applied": "Coupon Discount Applied", "offers.extra_off": "Extra {{redDeals}} off", "coupon.coupon_applied_successfully_msg": "Coupon is applied successfully.", "coupon.coupon_cant_appied": "Coupon cannot be applied.", "coupon.coupon_codes": "Coupon Codes", "coupon.loading_best_offers": "Loading best offers for you!", "coupon.enter_coupon_code": "Enter a coupon code", "coupon.apply": "APPLY", "coupon.not_applicable_promo": "Sorry! This promocode is not applicable at the moment.", "gst.gst_details_heading": "Enter GST Details", "gst.optional": "Optional", "gst.gstin_number": "GSTIN number", "gst.title": "Travel related details", "gst.heading": "GST Details", "gst.subheading": "Contact company HR to change the details", "gst.number": "GST NUMBER", "gst.company_name": "COMPANY NAME", "staff_vaccination.heading": "Your Safety Is Important", "govtid.cat_card_number": "CAT Card Number", "govtid.enter_cat_card_number": ": Enter CAT Card number", "insurace.secure_trip_heading": "Secure your trip", "insurance.benefits_msg": "See all the benefits you get for just {{insuranceAmtTxt}}/traveller", "insurance.yes": "Yes", "insurance.no": "No", "insurance.secure_my_trip": "Secure my trip", "insurance.risk_msg": "I'll take the risk", "insurance.proceeding_label": "By proceeding, I Agree to MakeMyTrip's", "insurance.terms_and_condition": "Terms and Conditions", "insurance.privacy_policy": "Privacy Policy", "insurance.terms_condition_msg": "By adding insurance you agree to the", "insurance.terms_condition": "Terms & Conditions", "insurance.kindly_select": "Kindly select", "insurance.or": "or", "insurance.above_to_continue": "above to continue", "insurance.user_agreement": "User Agreement", "insurance.terms_of_service": "Terms of Service", "insurance.and": "and", "retention.voucher_heading": "Your Gift Voucher", "retention.view_tc": "View T&Cs", "retention.user_message": "Please wait while we fetch the best offers for you...", "retention.use_coupon_msg": "Use coupon code", "thankyou.congratulations": "Congratulations!", "thankyou.booking_confirmed": "Your booking is confirmed successfully!", "thankyou.ticket_details_msg": "Tickets also sent to {{email}} and WhatsApp number {{phone}}", "thankyou.booking_id": "Booking ID :", "thankyou.pnrno": "PNR NO. - ", "thankyou.book_return_trip": "Book Your Return Trip!", "thankyou.instant_discount": "Get instant {{discount}}% off", "thankyou.book_now": "BOOK NOW", "thankyou.total_price": "Total Price", "thankyou.bus_advisory": "We wish to remind you that MakeMyTrip never asks for your personal banking and security details like passwords, OTP etc; or requests you to install third-party apps to view your screen. For any queries, please reach-out to us via the Help section on our mobile app or call on our official toll-free customer support numbers:", "thankyou.important_information": "IMPORTANT INFORMATION", "thankyou.manage_booking": "MANAGE BOOKINGS", "thankyou.kind_attention": "KIND ATTENTION", "thankyou.need_help": "Need Help?", "thankyou.travellers": "Travellers", "thankyou.booking_cant_complete": "Booking couldn’t be completed", "thankyou.refund_msg": "We are sorry! We will refund the full amount of {{total}} to your original payment mode within next 7 working days", "thankyou.error_label": "We are trying to confirm with the bus operator and share the ticket via email & sms. Please wait for 10-15 mins before booking again", "thankyou.bus_from_to": "Bus from {{from}} to {{to}}", "thankyou.have_queries": "Have queries regarding Cancellation Charges, Journey Details etc? You can manage your bookings in My Trips", "thankyou.bus_summary": "Bus Summary", "thankyou.safety_line": "You have chosen a bus that ensures amenities for safe travel & social distancing.", "thankyou.booking_exp": "How was your booking experience?", "thankyou.fetching_booking": "Fetching Booking details...", "thankyou.no_buses_for_applied_filter": "We couldn't find any buses for the applied filters", "thankyou.no_buses_found": "No buses found", "thankyou.change_filter": "CHANGE FILTERS", "calender.select": "Select", "calender.date": "Date", "farebreakup.heading": "<PERSON>e Breakup", "farebreakup.adult_fare": "Adult Base Fare/Seat", "farebreakup.child_fare": "Child Base Fare/Seat", "farebreakup.base_fare": "Base Fare", "farebreakup.fee_surcharges": "Fee & Surcharges", "farebreakup.other_charges": "Other Charges", "farebreakup.total_amount": "Total Amount", "holdbookingloader.not_started": "Confirming availability of your seats...", "holdbookingloader.in_progress": "Holding your seats...", "holdbookingloader.done": "Taking you to payments...", "holdbookingloader.fare_updated": "Bus operator has updated the fare...", "holdbookingloader.init_approval": "Please wait as we process your request...", "busreview.reviews": "Reviews", "busreview.review2": "reviews", "busreview.sorted_by": "Sorted by", "busreview.most_recent": "Most Recent", "busreview.overall_rating": "Overall Rating", "busreview.total_rating": "out of {{total_rating}}", "busreview.show_more": "SHOW MORE", "busreview.show_less": "SHOW LESS", "busreview.coupon_error_message": "Please enter a valid coupon", "busreview.horizontal_sleeper": "<PERSON><PERSON> Sleeper", "busreview.vertical_sleeper": "Vertical Sleeper", "busreview.login_now": "Login Now to explore more offers", "busreview.login_now2": "Login now to avail exciting offers", "busreview.safety_govt_guidelines": "Safety & Govt Guidelines", "busreview.seat_selected": "{{selectedSeatCount}} Seats selected", "busreview.seat_added": "{{selectedSeatCount}} Seats Added", "busreview.coupon_applied_msg": "Coupon applied sucessfully.", "busreview.fdc": "Free Date Change until {{time}}", "busreview.fdc.no_questions_asked": "No questions asked", "busreview.seater": "<PERSON><PERSON>", "busreview.select_travellers": "Select Travellers", "busreview.no_other_females_inlist": "No other females in your travellers list!", "busreview.no_other_males_inlist": "No other males in your travellers list!", "busamenities.amenities": "Amenities", "seatmap.rest_stop": "Rest Stops", "seatmap.start": "START", "seatmap.end": "END", "seatmap.stop": "Stop", "buserrors.no_bus.errorTitle": "No bus found on this route", "buserrors.no_bus.errorSubtitle": "Sorry, please try searching for different cities", "buserrors.no_bus.ctaText": "BOOK A TRAIN", "buserrors.no_bus.ctaDesc": "Alternatively, you may consider booking a train", "buserrors.alternate_means.errorTitle": "No bus found on this route", "buserrors.alternate_means.errorSubtitle": "Please wait, while we check for alternate means", "buserrors.unknown.errorMessage": "Something went wrong", "buserrors.unknown.errorDesc": "We are working on it, sorry!", "buserrors.unknown.errorSecondDesc": "Please search again after a while.", "buserrors.unknown.ctaText": "REFRESH", "buserrors.unknown_seo.errorMessage": "Uh oh!", "buserrors.unknown_seo.errorDesc": "Sorry, we could not fetch details for this bus", "buserrors.unknown_seo.view_other_buses": "VIEW OTHER BUSES", "buserrors.no_internet.errorMessage": "You are not connected to the Internet", "buserrors.no_internet.errorDesc": "Please check your Settings and try again.", "buserrors.no_reviews.errorMessage": "There are no user reviews currently for this bus.", "buserrors.no_reviews.errorDesc": "We will add some in due time", "buserrors.route_error.errorMessage": "No buses on this route", "buserrors.route_error.errorDesc": "We could not find any buses plying on your selected route", "buspolicies.policies": "Policies", "buspolicies.cancellation_policies": "Cancellation Policies", "buspolicies.cancellation_time": "Cancellation Time", "buspolicies.before_departure": "before departure", "buspolicies.penalty": "Penalty", "buspolicies.date_change_policy": "Date Change Policy", "buspolicies.time_before_travel": "Time Before Travel", "bus.gender_model.title": "Please change your seat selection", "bus.gender_model.body": "The bus operator has seats reserved for {{gender}} and we cannot book any other seat for you. Kindly select any of the seats highlighted for you by clicking 'Change Seat'.", "bus.gender_model.action_required": "Mandatory Action Required", "bus.gender_model.change_bus": "Change Bus", "bus.gender_model.chane_seat": "CHANGE SEAT", "default_cities": "{\"data\":{\"documents\":[{\"id\":\"MMTCC1092\",\"dn\":\"Bangalore,Karnataka\",\"lt\":\"city\",\"n\":\"Bangalore\",\"p\":\"Karnataka\",\"lat\":12.9715987,\"lon\":77.5945627},{\"id\":\"MMTCC1159\",\"dn\":\"Chennai,TamilNadu\",\"lt\":\"city\",\"n\":\"Chennai\",\"p\":\"TamilNadu\",\"lat\":13.0826802,\"lon\":80.2707184},{\"id\":\"MMTCC1319\",\"dn\":\"Hyderabad,Telangana\",\"lt\":\"city\",\"n\":\"Hyderabad\",\"p\":\"Telangana\",\"lat\":17.385044,\"lon\":78.486671},{\"id\":\"MMTCC1199\",\"dn\":\"Delhi\",\"lt\":\"city\",\"n\":\"Delhi\",\"p\":\"DelhiState\",\"lat\":28.7040592,\"lon\":77.1024902},{\"id\":\"MMTCC1744\",\"dn\":\"Pune,Maharashtra\",\"lt\":\"city\",\"n\":\"Pune\",\"p\":\"Maharashtra\",\"lat\":18.5204303,\"lon\":73.8567437},{\"id\":\"MMTCC1015\",\"dn\":\"Ahmedabad,Gujarat\",\"lt\":\"city\",\"n\":\"Ahmedabad\",\"p\":\"Gujarat\",\"lat\":23.022505,\"lon\":72.5713621},{\"id\":\"MMTCC1599\",\"dn\":\"Mumbai,Maharashtra\",\"lt\":\"city\",\"n\":\"Mumbai\",\"p\":\"Maharashtra\",\"lat\":19.0759837,\"lon\":72.8776559},{\"id\":\"MMTCC1184\",\"dn\":\"Coimbatore,TamilNadu\",\"lt\":\"city\",\"n\":\"Coimbatore\",\"p\":\"TamilNadu\",\"lat\":11.0168445,\"lon\":76.9558321},{\"id\":\"MMTCC1324\",\"dn\":\"Indore,MadhyaPradesh\",\"lt\":\"city\",\"n\":\"Indore\",\"p\":\"MadhyaPradesh\",\"lat\":22.7195687,\"lon\":75.8577258},{\"id\":\"MMTCC1333\",\"dn\":\"Jaipur,Rajasthan\",\"lt\":\"city\",\"n\":\"Jaipur\",\"p\":\"Rajasthan\",\"lat\":26.9124336,\"lon\":75.7872709}]},\"t\":0,\"success\":true}"}