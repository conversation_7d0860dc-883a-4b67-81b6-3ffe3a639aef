import { label, getCurrent<PERSON><PERSON><PERSON><PERSON>, EN} from './';
import { Dimensions } from "react-native";
import _isEmpty from 'lodash/isEmpty';

const baseWidth = 412;
const baseHeight = 869;
const MIN_FONT_SIZE = 11;

const { width, height } = Dimensions.get("window");

const getScaledDimension = (dimension, dimensionType) => {
  let ratio = 1;
  switch (dimensionType) {
    case 'height':
      ratio = height/baseHeight;
      break;
    case 'width':
    case 'font':
      ratio = width/baseWidth;
      break;
    default:
      ratio = 1;
      break;
  }
  return ratio * dimension;
}
const fontWeight = {
  eng: {
    regular: "Lato-Regular",
    medium: "Lato-Medium",
    semiBold: "Lato-Semibold",
    bold: "Lato-Bold",
    black: "Lato-Bold",
  },
  hin: {
    light: "Mukta-Regular",
    regular: "Mukta-Regular",
    medium: "Mukta-Medium",
    semiBold: "Mukta-Medium",
    bold: "Mukta-Bold",
    black: "Mukta-Bold"
  },
  tam: {
    regular: "Lato-Regular",
    medium: "Lato-Medium",
    semiBold: "Lato-Semibold",
    bold: "Lato-Bold",
    black: "Lato-Bold",
  },
}

const fontFamily = {
  eng: `'Lato', sans-serif`,
  hin: `'Mukta', sans-serif`,
  tam: `'Lato', sans-serif`
}

const lineHeightMultiplier = {
  eng: 1.2,
  hin: 1.4,
  tam: 1.4,
};

const fontSizeMultiplier = {
  eng: 1,
  hin: 0.9,
  tam: 0.8
}

export const fontStyle = (key) => {
  const lang = getCurrentLangKey();
  return {
    fontFamily: fontWeight[lang][key]
  }
};

function getScaledLineHeight (fontSize, lang) {
  return Math.trunc(fontSize * lineHeightMultiplier[lang]);
}

export const getLineHeight = (fontSize) => {
  const lang = getCurrentLangKey();
  const scaledFont = getScaledDimension(fontSize, "font") * fontSizeMultiplier[lang];
  const scaledFontSize = Math.max(MIN_FONT_SIZE, Math.round(scaledFont));
  return {
    lineHeight: getScaledLineHeight(scaledFontSize, lang),
    fontSize: scaledFontSize 
  }
};

export const isOnlyEnglishCharacters = (text = "") => {
    const isAsciiChar = (letter) => {
        const charCode = letter.charCodeAt();
        return ( (charCode >= 0) && (charCode < 128) );
    }

    // check if each character is in ASCII charSet
    return _isEmpty(text) || [...text].every((letter) => isAsciiChar(letter));
}

export const checkEnglishKeyboard = (errorType, text, thisPointer = null, setEnglishOnlyError) => {
  const valueToSet = (isOnlyEnglishCharacters(text)) ? "" : label("type_in_english");
  if(thisPointer) {
    thisPointer.setState({[errorType] : valueToSet});
  }
  if(setEnglishOnlyError) {
    setEnglishOnlyError(valueToSet);
  }
}

export const trimText = (str, limit) => {
  if (str.length <= 20) return str;
  return str.slice(0, limit).concat("...");
}
