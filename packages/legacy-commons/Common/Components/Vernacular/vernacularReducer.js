import { ACTION_SET_VERNACULAR_CONFIG, FETCH_LANGUAGE_DONE } from './vernacularActions';

const initState = {
  status: 'NOT_DONE',
  lang: 'eng',
  fetchingLanguage: true,
  showLanguagePicker: false,
};

export default function (state = initState, action) {
  switch (action.type) {
    case FETCH_LANGUAGE_DONE:
      return {
        ...state,
        fetchingLanguage: false,
        ...action.payload,
      };
    case ACTION_SET_VERNACULAR_CONFIG:
      return {
        ...state,
        ...action.payload,
      };
    default:
      return {
        ...state,
      };
  }
}
