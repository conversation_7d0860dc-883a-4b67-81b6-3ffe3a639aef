import React from 'react';
import {
  ActivityIndicator,
  Dimensions,
  StyleSheet,
  TextInput,
  View,
} from 'react-native';
import PropTypes from 'prop-types';
import { colors } from '../../../Styles/globalStyles';

const trimText = (str, limit) => {
  if (str.length <= 20) return str;
  return str.slice(0, limit).concat('...');
}

class AutoCompleteInputRails extends React.Component {
  focus = () => {
    if (this._textInput) {
      this._textInput.focus();
    }
  };
  clear = () => {
    if (this._textInput) {
      this._textInput.clear();
    }
  };
  blur = () => {
    if (this._textInput) {
      this._textInput.blur();
    }
  };

  render() {
    const { showClear, onClear, showLoading, useClearIcon, fromStation, labelStyle, placeholder, onChangeText, onFocus, id, style, ...textInputProps } = this.props;
    return (
      <View style={styles.container}>
        <View style={[styles.textInput]}>
          <TextInput
            {...textInputProps}
            placeholder={trimText(placeholder, 28)}
            onFocus={onFocus}
            onChangeText={onChangeText}
            ref={(e) => {
              this._textInput = e;
            }}
            testID={id}
            numberOfLines={1}
            style={[style]}
          />
        </View>
        <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', alignSelf: 'flex-end' }}>
          {showLoading && <ActivityIndicator color="#008b8b" />}
        </View>
      </View>
    );
  }
}

AutoCompleteInputRails.propTypes = {
  ...TextInput.PropTypes,
  underlineColorAndroid: PropTypes.string,
  showClear: PropTypes.bool,
  useClearIcon: PropTypes.bool,
  showLoading: PropTypes.bool,
  onClear: PropTypes.func.isRequired
};

AutoCompleteInputRails.defaultProps = {
  showClear: true,
  useClearIcon: true,
  showLoading: false,
  underlineColorAndroid: colors.transparent,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  containerSelected: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    backgroundColor: '#f1f8fe'
  },
  textInput: {
    flex: 4,
    width: '100%',
    marginLeft: 48,
    marginTop: -10,
    flexDirection: 'column',
  },
  clearIcon: {
    position: 'relative',
    right: 10,
    height: 20,
    width: 20,
  }
});

export default AutoCompleteInputRails;
