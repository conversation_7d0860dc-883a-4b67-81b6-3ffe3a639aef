import React from 'react';
import {
  ActivityIndicator,
  Image,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import PropTypes from 'prop-types';
import clearIcon from '@mmt/legacy-assets/src/ic_clear_black.webp';
import {colors, fonts} from '../../../Styles/globalStyles';
import TouchableRipple from '../TouchableRipple';

class AutoCompleteInput extends React.Component {
  focus = () => {
    if (this._textInput) {
      this._textInput.focus();
    }
  };
  clear = () => {
    if (this._textInput) {
      this._textInput.clear();
    }
  };
  blur = () => {
    if (this._textInput) {
      this._textInput.blur();
    }
  };

  render() {
    const {
      showClear, onClear, showLoading, useClearIcon, ...textInputProps
    } = this.props;
    return (
      <View style={styles.container}>
        <View style={styles.textInput}>
          <TextInput
            {...textInputProps}
            ref={(e) => {
              this._textInput = e;
            }}
          />
        </View>
        {showLoading &&
        <ActivityIndicator style={styles.clearIcon} color={colors.black} />
        }
        {!showLoading && showClear && useClearIcon &&
        <TouchableRipple onPress={onClear}>
          <Image source={clearIcon} style={styles.clearIcon} />
        </TouchableRipple>
        }
        {!showLoading && showClear && !useClearIcon &&
        <TouchableOpacity style={styles.backBtn} onPress={onClear}>
          <View>
            <Text style={styles.clearText}>CLEAR</Text>
          </View>
        </TouchableOpacity>
        }
      </View>);
  }
}

AutoCompleteInput.propTypes = {
  ...TextInput.PropTypes,
  underlineColorAndroid: PropTypes.string,
  showClear: PropTypes.bool,
  useClearIcon: PropTypes.bool,
  showLoading: PropTypes.bool,
  onClear: PropTypes.func.isRequired
};

AutoCompleteInput.defaultProps = {
  showClear: true,
  useClearIcon: true,
  showLoading: false,
  underlineColorAndroid: colors.transparent
};

const styles = StyleSheet.create({
  container: {
    flex: Platform.OS === 'web' ? 1 : 0,
    flexDirection: 'row',
    alignItems: 'center'
  },
  textInput: {
    flex: 1
  },
  clearIcon: {
    position: 'relative',
    right: 10,
    height: 20,
    width: 20
  },
  clearText: {
    color: colors.black,
    fontFamily: fonts.bold,
    fontSize: 10,
    paddingRight: 10
  }
});

export default AutoCompleteInput;
