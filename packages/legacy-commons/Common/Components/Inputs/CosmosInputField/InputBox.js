import React, {useState, useRef, useEffect} from 'react';
import {Animated, Image, Platform, StyleSheet, TextInput, TouchableOpacity, View, Text} from 'react-native';
import _ from 'lodash';
import {colors, fonts} from '../../../../Styles/globalStyles';

export default function InputBox({
  title,
  multiline,
  keyboardType,
  value,
  onChange,
  onFocus,
  onBlur,
  error,
  prefix,
  suffix,
  readOnly,
  onClick,
  icon,
  iconStyles,
  inputStyles,
}) {
  const [isInputActive, setIsInputActive] = useState(false);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const inputRef = useRef(null);

  const onInputFocus = () => {
    if (!readOnly) {
      setIsInputActive(true);
      inputRef.current.focus();
      onFocus();
    }
  };
  const onInputBlur = () => {
    setIsInputActive(false);
    onBlur();
  };
  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: isInputActive || !!value ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isInputActive, value, animatedValue]);
  let labelStyle = {
    position: 'absolute',
    left: 10,
    top: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [15, 5],
    }),
    fontSize: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [14, 11],
    }),
    color: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [styles.readOnlyText.color, isInputActive ? styles.activeInputTitle.color : (error ? styles.error.color : (_.isNull(value) || _.isEmpty(value) ? styles.activeInputTitle.color : '#9b9b9b'))],
    }),
  };
  return (
    <>
      <TouchableOpacity
        onPress={() => {
          onInputFocus();
          onClick();
        }}
        activeOpacity={0.75}>
        <View
          style={[
            styles.boxWrapper,
            isInputActive ? styles.activeInput : (error ? {borderColor: styles.error.color} : (_.isNull(value) || _.isEmpty(value) ? styles.activeInput : {})),
            Platform.OS === 'android' ? styles.noBottomPadding : {},
          ]}>
          <Animated.Text
            style={[
              labelStyle,
              styles.boxTitle,
              isInputActive ? styles.activeInput : (error ? {color: styles.error.color} : (_.isNull(value) || _.isEmpty(value) ? styles.activeInputTitle : {})),
            ]}>
            {title}&nbsp;
          </Animated.Text>
          <View style={styles.contentWrapper}>
            <View
              style={[
                Platform.OS === 'ios' && !multiline
                  ? { paddingTop: 6 }
                  : {},
                { flexDirection: 'row', alignItems: 'center', flex: 1},
              ]}>
              {(isInputActive || !!value) && prefix && (
                <Text style={styles.prefix}>{prefix}</Text>
              )}
              <TextInput
                ref={inputRef}
                style={[
                  styles.input,
                  inputStyles || {},
                ]}
                multiline={multiline}
                onFocus={onInputFocus}
                onBlur={onInputBlur}
                keyboardType={keyboardType || 'default'}
                value={value}
                onChangeText={onChange}
                editable={!readOnly}
                autoCorrect={false}
                autoCapitalize={'none'}
              />
              {(isInputActive || !!value) && suffix && (
                <Text style={{...styles.prefix, ...styles.suffix}}>
                  {suffix}
                </Text>
              )}
            </View>
            {icon && (
              <View style={styles.iconWrapper}>
                <Image source={icon} style={[iconStyles]} />
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
      {!isInputActive && !_.isNull(error) && !_.isEmpty(error) && <Text style={styles.error}>{error}</Text>}
    </>
  );
}

const styles = StyleSheet.create({
  noBottomPadding: {
    paddingBottom: 0,
  },
  boxWrapper: {
    paddingTop: 14,
    paddingBottom: 0,
    paddingHorizontal: 10,
    backgroundColor: colors.grey14,
    borderWidth: 1,
    borderColor: colors.greyBookedSeat,
    borderRadius: 2,
    height: 48,
    overflow: 'hidden',
  },
  boxTitle: {
    textTransform: 'uppercase',
    fontFamily: fonts.bold,
  },
  input: {
    fontSize: 16,
    fontFamily: fonts.black,
    fontWeight: Platform.OS === 'android' ? 'normal' : '900',
    paddingVertical: 0,
    margin: 0,
    color: colors.black,
  },
  inactiveInput: {
    position: 'relative',
    zIndex: 1,
    flex: 1,
    height: 48,
    marginTop: -16,
  },
  error: {
    color: colors.red,
    marginTop: 4,
  },
  activeInput: {
    backgroundColor: colors.white,
    borderColor: colors.inputActiveBrdr,
  },
  activeInputTitle: {
    color: colors.yellow2,
  },
  prefix: {
    fontSize: 16,
    fontFamily: fonts.black,
    marginRight: 8,
    color: colors.lightTextColor,
  },
  suffix: {
    marginRight: 0,
    marginLeft: 2,
  },
  readOnlyText: {
    color: colors.lightTextColor,
  },
  contentWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  iconWrapper: {
    flexShrink: 0,
    marginLeft: 4,
    paddingHorizontal: 4,
    marginBottom: 8,
    justifyContent: 'center',
  },
});

InputBox.defaultProps = {
  onFocus: () => {},
  onBlur: () => {},
  onClick: () => {},
};
