import React from 'react';
import {Image, View} from 'react-native';
import {normalisePx} from '../../Styles/globalStyles';

// eslint-disable-next-line import/no-unresolved
const saveIcon = require('@mmt/legacy-assets/src/ic_save_tag.webp');

const SaveIcon = () => (
  <View>
    <Image style={{width: normalisePx(37), height: normalisePx(20)}} source={saveIcon} />
  </View>
);


export default SaveIcon;
