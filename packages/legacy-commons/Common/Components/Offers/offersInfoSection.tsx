import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import iconArrowBlue from '@mmt/legacy-assets/src/iconArrowBlue.webp'
import { colors, fonts } from '../../../Styles/globalStyles';

interface Props {
  offerMsg: string | any,
  showIcon?: boolean
}

const OfferInfo: React.FunctionComponent<Props> = ({ offerMsg = 'Book Now', showIcon }) => {
  return (
    <>
      <View style={styles.offerCta}>
        <View style={styles.offersCtaContainer}>
          <Text style={styles.message}>
            {offerMsg}
          </Text>
          {showIcon && <Image source={iconArrowBlue} style={styles.arrowImg} />}
        </View>
      </View>
    </>
  )
}

export default OfferInfo;

const styles = StyleSheet.create({
  message: {
    fontSize: 12,
    lineHeight: 20,
    paddingVertical: 8,
    color: colors.azure,
    fontWeight: '700',
    textTransform: 'uppercase',
    fontFamily: fonts.regular
  },
  offerCta: {
    backgroundColor: colors.white,
    flex: 1,
    width: '100%',
    flexDirection: 'row-reverse',
  },
  offersCtaContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingRight: 13
  },
  arrowImg: {
    width: 13,
    height: 13,
    marginTop: 2,
    marginLeft: 5
  },
})
