import React from 'react';
import { Image, ScrollView, Text, View, Platform, StyleSheet } from 'react-native';
import { colors, fonts } from '../../../Styles/globalStyles';
import OfferCard from './OfferCard';
interface Props {
  offers: [{
    skyImageUrl: string,
    heroUrl: string,
    newHeroUrl: string,
    promoMessage: string
    promoTitle: string,
    id: number
  }];
  onItemClick: () => void;
  id: string;
  headerText: string;
}
const OfferList = ({ offers, onItemClick, id, headerText }: Props) => {
  if (!offers || !offers?.length) {
    return null;
  }
  return (
    <View style={offerStyles.containerStyle}>
      <View style={offerStyles.headerContainer}>
        <Image source={require('@mmt/legacy-assets/src/ic_offer_rails.webp')} style={offerStyles.offerImage} />
        <Text style={[offerStyles.titleText, { fontFamily: fonts.black }]} testID={id}>
          {headerText}
        </Text>
      </View>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={offerStyles.containerPadding}>
        {offers.map((offer, index) => (
          <OfferCard key={offer.id + index} item={offer} onItemClick={onItemClick} />
        ))}
      </ScrollView>
    </View>
  );
};

export const offerStyles = StyleSheet.create({
  containerStyle: {
    backgroundColor: colors.white,
  },
  containerDimen: {
    width: 258,
    minHeight: 135,
    borderRadius: 4,
  },
  titleText: {
    fontSize: 12,
    color: colors.black,
    textTransform: 'uppercase'
  },
  viewText: {
    fontSize: 12,
    color: colors.azure,
    textTransform: 'uppercase'
  },
  viewAllImageHolder: {
    flexDirection: 'row',
    marginLeft: 220,
    alignSelf: 'flex-end',
    width: 130,
    height: 30
  },
  viewAllImage: {
    width: 20,
    height: 20,
    marginTop: -3
  },
  offerImage: {
    width: 20,
    height: 20,
    marginTop: -3,
    marginRight: 5,
  },
  headerContainer: {
    flexDirection: 'row',
    marginHorizontal: 14,
    marginTop: 10
  },
  detailsArrowImage: {
    width: 34,
    height: 34,
    marginBottom: 5,
    alignSelf: 'center'
  },
  containerPadding: {
    paddingHorizontal: 10,
    paddingVertical: 9
  },
  imageStyle: {
    marginHorizontal: 10,
    ...Platform.select({
      ios: {
        marginHorizontal: 4,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.3,
        shadowRadius: 2,
        borderRadius: 2,
        backgroundColor: 'transparent',
        minHeight: 135,
        marginTop: 4
      },
      android: {
        minHeight: 141,
        marginTop: 2
      }
    })
  },
  lineGreen: {
    width: 3,
    height: 13,
    backgroundColor: colors.lightGreen4,
    position: 'absolute',
    marginTop: 12,
    ...Platform.select({
      web: {
        marginLeft: -10,
        marginTop: 8
      }
    })
  },
  textTitle: {
    fontSize: 16,
    color: colors.black
  },
  textSubtitle: {
    fontSize: 12,
    color: colors.defaultTextColor,
    marginTop: 13
  },
  promoCodeContainer: {
    flexDirection: 'row',
    marginTop: 14,
    flex: 1,
    alignItems: 'flex-end'
  },
  promoCode: {
    fontSize: 12,
    color: colors.black
  },
  textTnc: {
    fontSize: 8,
    fontStyle: 'italic',
    color: colors.lightTextColor,
    flex: 1,
    textAlign: 'right'
  },
  boldText: {
  },
  gravityCenter: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  progressView: { width: 70, height: 10 }
});



export default OfferList;
