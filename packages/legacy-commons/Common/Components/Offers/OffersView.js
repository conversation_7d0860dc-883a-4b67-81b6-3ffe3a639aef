import React, { useRef } from 'react';
import { Image, ScrollView, Text, View } from 'react-native';
import PropTypes from 'prop-types';
import OfferListItem from './OfferListItem';
import styles from './FlashHeroOfferCss';
const Cabs = 'CABS';
const Bus = 'BUS';

const arrowRightLong = require('@mmt/legacy-assets/src/ic_arrow_right_long.webp');

const offersViewDefaultPropOnItemClick = () => {};

const OffersView = ({ offers, headerText, onItemClick = offersViewDefaultPropOnItemClick, lob }) => {
  const scrollViewRef = useRef(null);
  if (!offers || offers.length === 0) {
    return null;
  }
  return (
    <View
      style={[
        styles.container,
        // @TODO: take prop as extra container style
        lob === Bus || lob === Cabs ? { marginVertical: 8 } : { marginVertical: 16 },
      ]}
    >
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        ref={scrollViewRef}
        onContentSizeChange={() => {
          if (lob === Cabs) {
            scrollViewRef?.current?.scrollTo({
              x: 0,
            });
          }
        }}
      >
        <View style={styles.headerContainer}>
          <Text style={styles.titleText}>{headerText}</Text>
          <Image style={styles.arrowIcon} source={arrowRightLong} />
        </View>
        {offers.map((offer, index) => (
          <OfferListItem
            key={offer.promoTitle}
            item={offer}
            onItemClick={onItemClick}
            index={index}
          />
        ))}
      </ScrollView>
    </View>
  );
};

OffersView.propTypes = {
  offers: PropTypes.array.isRequired,
  headerText: PropTypes.string.isRequired,
  onItemClick: PropTypes.func,
  lob: PropTypes.string,
};

export default OffersView;
