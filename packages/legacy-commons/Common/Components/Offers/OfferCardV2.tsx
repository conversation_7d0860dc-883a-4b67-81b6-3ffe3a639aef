import React, { useState } from 'react';
import { View, Text, Image, StyleSheet, Platform, TouchableOpacity } from "react-native";
import TouchableRipple from '../TouchableRipple';
import { colors, fonts } from '../../../Styles/globalStyles';
import { getSkyImageUrl, getHeroImageUrl } from '../../utils/OffersUtils';
import { offersCommonStyles } from './offersGlobalStyles';
import OfferInfo from './offersInfoSection';
// import OfferInfo from '@mmt/legacy-commons/Common/Components/Offers/offersInfoSection';

const isMweb = () => Platform.OS === 'web';

interface Props {
  item: {
    skyImageUrl: string,
    heroUrl: string,
    newHeroUrl: string,
    promoMessage: string
    promoTitle: string,
  }
  onItemClick: () => void;
  infoTxt?: string;
  showIcon?: boolean;
}
const OfferCardV2: React.FunctionComponent<Props> = ({ item, onItemClick, infoTxt, showIcon }) => {
  const imageUrl = item?.skyBigFullImgUrl ? getSkyImageUrl(item.skyBigFullImgUrl) : getSkyImageUrl(item.skyImageUrl) || getHeroImageUrl(item.heroUrl || item.newHeroUrl);
  const [imageLoaded, setImageLoaded] = useState(false);
  const renderImage = () => {
    if (isMweb()) {
      return <img
        src={imageUrl}
        style={StyleSheet.flatten([styles.img])}
        loading='lazy'
      />
    } else {
      return <Image
        style={styles.img}
        onLoad={() => setImageLoaded(true)}
        resizeMode={'cover'}
        onLoadEnd={() => setImageLoaded(true)}
        source={{ uri: imageUrl }}
      />;
    }
  }

  return (
    <TouchableOpacity onPress={() => onItemClick(item)}>
      <View style={styles.card}>
        <View style={styles.imgContainer}>
          {renderImage()}
        </View>
        <OfferInfo offerMsg={infoTxt} showIcon={showIcon} />
      </View>
    </TouchableOpacity >
  )
}

export default OfferCardV2

const styles = StyleSheet.create({
  card: {
    display: 'flex',
    marginRight: 12,
    alignItems: 'center',
    maxWidth: 280,
    backgroundColor: colors.gray2,
    borderRadius: 16,
    flex: 1,
    overflow: 'hidden'
  },
  img: {
    width: 276,
    height: 156,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  imgContainer: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.cosomsGreyBorder,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    backgroundColor: colors.gray2,
    overflow: 'hidden',
  },

});