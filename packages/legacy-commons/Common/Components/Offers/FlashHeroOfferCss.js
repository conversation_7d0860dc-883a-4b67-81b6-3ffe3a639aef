import {colors, fonts} from '../../../Styles/globalStyles';
import {Platform} from 'react-native';

const styles = {
  containerDimen: {
    width: 258,
    minHeight: 135,
    marginHorizontal: 4
  },
  titleText: {
    fontFamily: fonts.bold,
    fontSize: 16,
    color: colors.defaultTextColor
  },
  headerContainer: {
    paddingStart: 14,
    paddingVertical: 14,
    width: 130
  },
  arrowIcon: {
    marginTop: 12
  },
  containerPadding: {
    paddingHorizontal: 9,
    paddingVertical: 9
  },
  imageStyle: {
    marginHorizontal: 0,
    ...Platform.select({
      ios: {
        marginHorizontal: 4,
        shadowColor: colors.black,
        shadowOffset: {width: 0, height: 1},
        shadowOpacity: 0.3,
        shadowRadius: 2,
        borderRadius: 2,
        backgroundColor: 'transparent',
        minHeight: 135,
        marginTop: 4
      },
      android: {
        minHeight: 141,
        marginTop: 2
      }
    })
  },
  lineGreen: {
    width: 3,
    height: 13,
    backgroundColor: '#33d18f',
    position: 'absolute',
    marginTop: 12,
    ...Platform.select({
      web: {
        marginLeft: -10,
        marginTop: 8
      }
    })
  },
  textTitle: {
    fontFamily: fonts.bold,
    fontSize: 16,
    color: colors.black
  },
  textSubtitle: {
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.defaultTextColor,
    marginTop: 13
  },
  promoCodeContainer: {
    flexDirection: 'row',
    marginTop: 14,
    flex: 1,
    alignItems: 'flex-end'
  },
  promoCode: {
    fontFamily: fonts.light,
    fontSize: 12,
    color: colors.black
  },
  textTnc: {
    fontFamily: fonts.regular,
    fontSize: 8,
    fontStyle: 'italic',
    color: colors.lightTextColor,
    flex: 1,
    textAlign: 'right'
  },
  boldText: {
    fontFamily: fonts.bold
  },
  gravityCenter: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  progressView: {width: 70, height: 10}
};
export default styles;
