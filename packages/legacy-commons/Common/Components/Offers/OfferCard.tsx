import React, { useState } from 'react';
import { View, Text, Image, StyleSheet, Platform } from "react-native";
// @ts-ignore
import TouchableRipple from '../TouchableRipple';
import { fonts } from '../../../Styles/globalStyles';
import { getSkyImageUrl, getHeroImageUrl } from '../../utils/OffersUtils';
import colors from 'packages/ui/base/colors';
const isIos = () => Platform.OS === 'ios';
const isMweb = () => Platform.OS === 'web';
interface Props {
  item: {
    skyImageUrl: string,
    heroUrl: string,
    newHeroUrl: string,
    promoMessage: string
    promoTitle: string
  }
  onItemClick: () => void;
}
const OfferCard = ({ item, onItemClick }: Props) => {

  const imageUrl = getSkyImageUrl(item.skyImageUrl) || getHeroImageUrl(item.heroUrl || item.newHeroUrl);
  const [imageLoaded, setImageLoaded] = useState(false);
  const renderImage = () => {
    if (isMweb()) {
      return <img
        src={imageUrl}
        style={StyleSheet.flatten([styles.img])}
        loading='lazy'
      />
    } else {
      return <Image
        style={styles.img}
        onLoad={() => setImageLoaded(true)}
        resizeMode={isIos() ? 'contain' : 'stretch'}
        onLoadEnd={() => setImageLoaded(true)}
        source={{ uri: imageUrl }}
      />;
    }
  }

  return (
    <TouchableRipple onPress={() => onItemClick(item)}>
      <View style={styles.card}>
        {renderImage()}
        <Text style={[styles.message, { fontFamily: fonts.black, color: colors.black }]}>
          {item.promoMessage}
        </Text>
      </View>
    </TouchableRipple>
  )
}

export default OfferCard

const styles = StyleSheet.create({
  card: {
    display: 'flex',
    flexDirection: 'column',
    marginRight: 10,
    alignItems: 'center',
    shadowColor: '#0003',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowRadius: 4,
    maxWidth: 150,
  },
  img: {
    height: 72,
    width: 150,
    borderRadius: 4,
    marginBottom: 5,
  },
  message: {
    fontSize: 12,
    lineHeight: 16,
    padding: 5,
  }
});