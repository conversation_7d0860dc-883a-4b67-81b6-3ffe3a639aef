import React from "react";
import { Image, Platform, Text, View } from "react-native";
import PropTypes from "prop-types";
import Card from "../Card";
import HorizontalProgressView from "../Progress/HorizontalProgressView";
import { getScreenDensityName } from "../../../Helpers/displayHelper";
import styles from "./FlashHeroOfferCss";
import TouchableRipple from "../TouchableRipple";

const ImageLoadingState = {
  LOADING: "loading",
  COMPLETED: "completed",
  ERROR: "error",
};

class OfferListItem extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      loadingState: ImageLoadingState.LOADING,
    };
    this.isImageLoaded = false;
  }

  componentDidMount() {
    this.prefetchImage();
  }

  async prefetchImage() {
    const url = this.getHeroImageUrl(this.props.item.newHeroOfferCardUrl);
    if (!url || url.length === 0) {
      this.setState({ loadingState: ImageLoadingState.ERROR });
      return;
    }
    if (Platform.OS === "android") {
      const cacheData = await Image.queryCache([url]);
      if (cacheData[url]) {
        this.setState({ loadingState: ImageLoadingState.COMPLETED });
        return;
      }
    }
    await Image.prefetch(url)
      .then(() => {
        this.setState({ loadingState: ImageLoadingState.COMPLETED });
      })
      .catch(() => {
        this.setState({ loadingState: ImageLoadingState.ERROR });
      });
  }

  render() {
    const { loadingState } = this.state;
    const { showTnc } = this.context;
    const { item, onItemClick, index } = this.props;
    const imageLoaded = loadingState !== ImageLoadingState.LOADING;

    return (
      <TouchableRipple
        feedbackColor="transparent"
        disabled={!imageLoaded}
        onPress={() => onItemClick(item, index)}
      >
        <View>
          {loadingState === ImageLoadingState.LOADING &&
            this.renderLoadingView()}
          {loadingState === ImageLoadingState.COMPLETED &&
            this.renderImage(item)}
          {loadingState === ImageLoadingState.ERROR &&
            this.renderCardData(item, showTnc)}
        </View>
      </TouchableRipple>
    );
  }

  renderCardData = (item, showTnc) => (
    <Card style={{ ...styles.containerDimen, ...styles.containerPadding }}>
      <View style={styles.lineGreen} />
      <Text numberOfLines={2} style={styles.textTitle}>
        {item.promoTitle}
      </Text>
      <Text numberOfLines={2} style={styles.textSubtitle}>
        {item.promoMessage}
      </Text>
      <View style={styles.promoCodeContainer}>
        {item.promoCode && (
          <Text style={styles.promoCode}>
            CODE <Text style={styles.boldText}>{item.promoCode}</Text>
          </Text>
        )}
        {showTnc && <Text style={styles.textTnc}>*Tap to View T&Cs</Text>}
      </View>
    </Card>
  );

  renderImage = (item) => {
  if( Platform.OS === 'web'){
      return <img
      src={this.getHeroImageUrl(item.newHeroOfferCardUrl) || "null" }
      style={{...styles.containerDimen, ...styles.imageStyle}}
      loading='lazy'
    />
  } else { 
      return <Image
      style={[styles.containerDimen, styles.imageStyle]}
      onLoad={this.onLoad}
      resizeMode={Platform.OS === "ios" ? "contain" : "stretch"}
      onLoadEnd={this.onLoadEnd}
      source={{ uri: this.getHeroImageUrl(item.newHeroOfferCardUrl) || "null" }}
      />;
    }
  }

  onLoad = () => {
    this.isImageLoaded = true;
  };

  onLoadEnd = () => {
    if (!this.isImageLoaded) {
      this.setState({ loadingState: ImageLoadingState.ERROR });
    }
  };

  renderLoadingView = () => (
    <Card style={{ ...styles.containerDimen, ...styles.gravityCenter }}>
      <HorizontalProgressView style={styles.progressView} />
    </Card>
  );

  getHeroImageUrl = (imageURL) => {
    if (imageURL && (Platform.OS === "android" || Platform.OS === "web")) {
      const split = imageURL.split("appfest/");
      if (split.length >= 2 && split[0] && split[1]) {
        return "".concat(
          split[0],
          "appfest/",
          getScreenDensityName(),
          "/",
          split[1]
        );
      }
      return imageURL;
    }
    return imageURL || "";
  };
}
OfferListItem.propTypes = {
  item: PropTypes.object.isRequired,
  onItemClick: PropTypes.func.isRequired,
};
OfferListItem.contextTypes = {
  showTnc: PropTypes.bool,
};
export default OfferListItem;
