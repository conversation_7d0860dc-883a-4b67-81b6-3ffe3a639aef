import React from 'react';
import { Image, ScrollView, Text, View, Platform, StyleSheet, Linking } from 'react-native';
import { colors, fonts } from '../../../Styles/globalStyles';
import OfferCard from './OfferCard';
import OfferCardV2 from './OfferCardV2';
import OfferInfo from '@mmt/legacy-commons/Common/Components/Offers/offersInfoSection';

interface Props {
  offers: {
    skyImageUrl: string;
    heroUrl: string;
    newHeroUrl: string;
    promoMessage: string;
    promoTitle: string;
    id: number;
  }[];
  onItemClick: () => void;
  id: string;
  headerText: string;
  infoText: string;
  showIcon: boolean;
}
const OfferListV2 = ({ offers, onItemClick, id, headerText, infoText, showIcon }: Props) => {
  if (!offers || !offers?.length) {
    return null;
  }
  return (
    <View>
      <View style={offerStyles.headerContainer}>
        <Text style={offerStyles.titleText} testID={id}>
          {headerText}
        </Text>
      </View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={offerStyles.containerPadding}
      >
        {offers.map((offer, index) => (
          <OfferCardV2
            key={offer.id + index}
            item={offer}
            onItemClick={onItemClick}
            infoTxt={infoText}
            showIcon={showIcon}
          />
        ))}
      </ScrollView>
    </View>
  );
};

export const offerStyles = StyleSheet.create({
  containerDimen: {
    width: 258,
    minHeight: 135,
    borderRadius: 4,
  },
  titleText: {
    fontSize: 18,
    color: colors.black,
    lineHeight: 22,
    fontWeight: '900',
    fontFamily: fonts.black,
  },
  viewAllText: {
    fontSize: 10,
    color: colors.primary,
    lineHeight: 10,
    fontWeight: '600',
    fontFamily: fonts.regular,
    textTransform: 'uppercase',
    letterSpacing: 0.08,
  },
  viewText: {
    fontSize: 12,
    color: colors.azure,
    textTransform: 'uppercase',
  },
  viewAllImageHolder: {
    flexDirection: 'row',
    marginLeft: 220,
    alignSelf: 'flex-end',
    width: 130,
    height: 30,
  },
  viewAllImage: {
    width: 20,
    height: 20,
    marginTop: -3,
  },
  offerImage: {
    width: 20,
    height: 20,
    marginTop: -3,
    marginRight: 5,
  },
  headerContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginTop: 10,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailsArrowImage: {
    width: 34,
    height: 34,
    marginBottom: 5,
    alignSelf: 'center',
  },
  containerPadding: {
    paddingLeft: 16,
    paddingRight: 4,
    paddingVertical: 9,
  },
  imageStyle: {
    marginHorizontal: 10,
    ...Platform.select({
      ios: {
        marginHorizontal: 4,
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.3,
        shadowRadius: 2,
        borderRadius: 2,
        backgroundColor: 'transparent',
        minHeight: 135,
        marginTop: 4,
      },
      android: {
        minHeight: 141,
        marginTop: 2,
      },
    }),
  },
  lineGreen: {
    width: 3,
    height: 13,
    backgroundColor: colors.lightGreen4,
    position: 'absolute',
    marginTop: 12,
    ...Platform.select({
      web: {
        marginLeft: -10,
        marginTop: 8,
      },
    }),
  },
  textTitle: {
    fontSize: 16,
    color: colors.black,
  },
  textSubtitle: {
    fontSize: 12,
    color: colors.defaultTextColor,
    marginTop: 13,
  },
  promoCodeContainer: {
    flexDirection: 'row',
    marginTop: 14,
    flex: 1,
    alignItems: 'flex-end',
  },
  promoCode: {
    fontSize: 12,
    color: colors.black,
  },
  textTnc: {
    fontSize: 8,
    fontStyle: 'italic',
    color: colors.lightTextColor,
    flex: 1,
    textAlign: 'right',
  },
  boldText: {},
  gravityCenter: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressView: { width: 70, height: 10 },
});

export default OfferListV2;
