import React, { Component } from 'react';
import { ProgressViewIOS} from 'react-native';
import PropTypes from 'prop-types';

export default class HorizontalProgressView extends Component {

  constructor(props){
    super(props)
    this.state = {
      start: 0,
      distance: props.distance,
      progressStyle:props.style,
    };
    this.removed = false;
  }

  componentDidMount(){
    this.removed = true;
    setInterval(()=>{
      this.updateProgress()
    }, 80)
  }

  updateProgress = () => {
    if (this.removed) {
      var start = this.state.start + 0.1;
      start = start > 1 ? 0 : start
      this.setState({ start });
      if(start < this.state.distance) {
        requestAnimationFrame(() => this.updateProgress());
      }
    }
  }

  componentWillUnmount(){
    this.removed = false;
    clearTimeout(this.updateProgress)
  }

  render () {
        return (
            <ProgressViewIOS
              progress={this.state.start}
              style={this.state.progressStyle}
              progressViewStyle={'bar'}
              progressTintColor={'#009688'}
              trackTintColor={'#bde4e0'}
            />
          )
      }
  }
