import React, { FC, useState, useEffect, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { colors } from '../../../Styles/globalStyles';

import { Props } from './types';
import { Tab } from './components';

export const TabView: FC<Props> = ({ tabs, style, onTabActive, activeTabIndex = 0, testId = '' }) => {
  const [activeTab, setActiveTab] = useState<number>(activeTabIndex);

  const setActiveTabHandler = useCallback((value) => {
    setActiveTab(value);
    onTabActive?.(value);
  }, [onTabActive, setActiveTab]);

  useEffect(() => setActiveTab(activeTabIndex), [activeTabIndex]);

  return (
    <>
      <View style={[styles.row, style]}>
        {tabs.map(({ id, data, style: tabStyle }, index) =>
          <Tab
            testId={testId}
            key={id}
            index={index}
            style={tabStyle}
            activateTab={setActiveTabHandler}
            data={{
              ...data,
              ...(
                data.isDisabled
                  ? { status: 'disabled' }
                  : { status: activeTab === index ? 'active' : 'inactive' }
              ),
            }}
          />
        )}
      </View>
      <View style={styles.shadow} />
      {tabs[activeTab].view}
    </>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    height: 60,
  },
  shadow: {
    height: 13,
    backgroundColor: colors.grayBg,
  },
});

// please connect with @mmt9107, should any concerns/doubts arise
