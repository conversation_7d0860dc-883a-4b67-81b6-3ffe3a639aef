import { ViewStyle, StyleProp, ImageStyle, ImageSourcePropType } from 'react-native';

export interface Props {
  testId: string;
  index: number;
  style?: ViewStyle;
  activateTab: (index: number) => void;
  data: {
    offer?: string;
    heading: string;
    headingStyle?: ViewStyle;
    isLoading: boolean;
    subHeading?: string;
    subHeadingStyle?: ViewStyle;
    onPress?: () => void;
    isPressDisabled: boolean;
    status: 'active' | 'inactive' | 'disabled';
    icon: {
      style: StyleProp<ImageStyle>
      source: {
        active: ImageSourcePropType,
        inactive: ImageSourcePropType,
        disabled: ImageSourcePropType,
      },
    };
  },
}
