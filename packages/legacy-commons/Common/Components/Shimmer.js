import React from 'react';
import { StyleSheet, Animated, Easing } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

export default class Shimmer extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      fadeAnim: new Animated.Value(0.4),
      translateX: new Animated.Value(-100)
    };
  }

  componentDidMount() {
    this.animate();
  }

  animate() {
    Animated.loop(
      Animated.parallel([
        Animated.timing(this.state.fadeAnim, {
          toValue: 1,
          duration: 1500,
          easing: Easing.linear,
          useNativeDriver: true
        }),
        Animated.timing(this.state.translateX, {
          toValue: 200,
          duration: 1500,
          easing: Easing.linear,
          useNativeDriver: true
        })
      ]))
      .start();
  }

  render() {
    let { fadeAnim, translateX } = this.state;
    // Calculate interpolation for translateX
    const translateXInterpolation = translateX.interpolate({
      inputRange: [0, 1], // The Input range corresponds to initial and final values of translateX
      outputRange: [0, 1], // The Output range corresponds to 0 and 1 for marginLeft. Output range should be numerical values
    });

    return (
      <Animated.View
        style={[styles.animatedInner, {
          opacity: fadeAnim,
          transform: [{ translateX: translateXInterpolation }],
        }]}
      >
        <LinearGradient
          start={{ x: 1.0, y: 0.0 }}
          end={{ x: 0.0, y: 1.0 }}
          colors={['#f3f4f7', '#f3f4f7']}
        />
      </Animated.View>
    );
  }
}
const styles = StyleSheet.create({
  animatedInner: {
    width: '70%',
    height: '100%',
    backgroundColor: '#f0eeee'

  }
});
