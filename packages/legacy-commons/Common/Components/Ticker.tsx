import React, {useEffect, useRef, useState, useCallback} from 'react';
import {View, Text, ScrollView, Animated, StyleSheet} from 'react-native';

interface TickerProps {
  data: React.Component[],
  duration: number //ms,
  initialHeight?: number
}

const Ticker = ({data, duration = 2000, initialHeight = 15}: TickerProps) => {
  const [displayView, changeView] = useState(data[0]);
  const [displayCount, changeDisplayCount] = useState(0);
  const firstUpdate = useRef(true);
  let viewHeight: any  = useRef(0);
  let timer: any = useRef(null);
  const positionAnim = useRef(new Animated.Value(0.01)).current;
  const slideUp = useCallback(() => {
    Animated.timing(positionAnim, {
      toValue: 0,
      useNativeDriver: true,
      duration: 500
    }).start(() => {
      timer.current = setTimeout(() => {
        slideDown();
      }, duration)
    });
  }, []);

  const slideDown = useCallback(() => {
    Animated.timing(positionAnim, {
      toValue: viewHeight.current || initialHeight,
      useNativeDriver: true,
      duration: 500
    }).start(() => {
      firstUpdate.current = false;
      changeDisplayCount((prevCount) => {
        let nextCount = 0;
        if (prevCount === (data.length - 1)) {
          nextCount = 0;
        } else {
          nextCount = prevCount + 1;
        }
        return nextCount;
      });
    });
  }, []);

  useEffect(() => {
    slideUp();
    return () => {
      if (timer && timer.current)
        clearInterval(timer.current);
    }
  }, []);

  useEffect(() => {
    if (firstUpdate.current) {
      return;
    }
    changeView(data[displayCount]);
  }, [displayCount]);

  useEffect(() => {
    if (firstUpdate.current) {
      return;
    }
    slideUp()
  }, [displayView]);

  const getComponentHeight = (e: any) => {
    if (e.nativeEvent.layout.height)
      viewHeight.current = e.nativeEvent.layout.height;
  };

  return(
    <View style={{overflow: 'hidden'}}>
      <View style={styles.container}>
        <Animated.View
          useNativeDriver={true}
          style={{transform: [{ translateY: positionAnim }]}}
          onLayout={getComponentHeight}
        >
          {displayView}
        </Animated.View>
      </View>
    </View>
  );
};

export default React.memo(Ticker);

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
  }
});
