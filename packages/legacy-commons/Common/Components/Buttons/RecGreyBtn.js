import React from 'react';
import {Text, StyleSheet, View} from 'react-native';
import PropTypes from 'prop-types';
import AtomicCss from '../../../Styles/AtomicCss';


class RecGreyBtn extends React.PureComponent {
  render() {
    return (
      <View style={[styles.RecGreyBtn, AtomicCss.marginBottom10, AtomicCss.marginTop10]}>
        <Text style={[styles.btnText, AtomicCss.blackFont]}>{this.props.label}</Text>
      </View>
    );
  }
}


RecGreyBtn.propTypes = {
  label: PropTypes.string.isRequired
};

const styles = StyleSheet.create({
  RecGreyBtn: {
    paddingVertical: 18,
    marginHorizontal: 10,
    borderRadius: 4,
    overflow: 'hidden',
    backgroundColor: '#c2c2c2'
  },
  btnText: {textAlign: 'center', fontSize: 16, color: '#e6e6e6'}
});

export default RecGreyBtn;
