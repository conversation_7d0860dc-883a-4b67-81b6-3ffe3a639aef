import React, {Component} from 'react';
import {TouchableOpacity, Text, StyleSheet, Image, View, Platform} from 'react-native';
import AtomicCss from '../../../../Styles/AtomicCss.js';
import ratingStarIcon from '@mmt/legacy-assets/src/ratingStar.webp';
import PropTypes from 'prop-types';


class ToggleBtn extends Component {
  constructor(props) {
    super(props);
  }

  onToggle = () => {

    const {filterObj, onToggle, uniqueId, isDisabled} = this.props;

    if (!isDisabled) {
      if (filterObj) {
        onToggle(filterObj.id, uniqueId, filterObj.urlParam);
      } else {
        onToggle(uniqueId);
      }
    }
  };


  render() {

    const {label, isActive, isDisabled, hotelStar} = this.props;
    const activeBtnOpacity = 0.7;
    const containerStyle = [styles.container];
    const textStyle = [styles.text];


    if (isActive) {
      containerStyle.push(styles.activeContainer);
      textStyle.push(styles.activeText);
    }
    if (isDisabled) {
      containerStyle.push(styles.buttonDisabled);
      textStyle.push(styles.inActiveText);
    }

    return (
      <TouchableOpacity style={containerStyle} onPress={this.onToggle}
                        activeOpacity={activeBtnOpacity}>
        <View style={AtomicCss.flexRow}>
          <Text style={textStyle}>{label} </Text>
          {hotelStar
          && <View style={AtomicCss.flexRow}><Image style={styles.iconRatingStar}
                                                    source={ratingStarIcon}/>
            <Text style={textStyle}>Hotel</Text></View>}
        </View>
      </TouchableOpacity>
    );
  }
};

ToggleBtn.propTypes = {
  filterObj: PropTypes.object,
  onToggle: PropTypes.func.isRequired,
  label: PropTypes.string.isRequired,
  isActive: PropTypes.bool,
  uniqueId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  isDisabled: PropTypes.bool,
  hotelStar: PropTypes.bool
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 4,
    elevation: 2,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    ...Platform.select({
      ios: {
        shadowRadius: 2
      },
      android: {
        shadowRadius: 2
      },
      web: {
        shadowRadius: 5
      }
    }),
    shadowOffset: {
      width: 0,
      height: 0
    },
    margin: 5,
    backgroundColor: '#fff'
  },
  activeContainer: {
    backgroundColor: '#c4e4fe',
    shadowOpacity: 0,
    elevation: 0
  },
  activeText: {
    fontFamily: 'Lato-Regular',
    fontWeight: '900',
    letterSpacing: 0,
    color: '#008cff'
  },
  inActiveText: {
    fontFamily: 'Lato-Regular',
    opacity: 0.5,
    letterSpacing: 0,
    color: '#9b9b9b'
  },
  text: {
    fontFamily: 'Lato-Regular',
    fontSize: 12,
    letterSpacing: 0,
    color: '#9b9b9b'
  },
  iconRatingStar: {
    width: 11,
    height: 11,
    marginLeft: -3,
    marginTop: 1,
    marginRight: 4
  },
  buttonDisabled: {
    backgroundColor: '#ffffff'
  }
});

export default ToggleBtn;
