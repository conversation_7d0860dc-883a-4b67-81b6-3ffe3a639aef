import React from 'react';
import {TouchableOpacity, StyleSheet, Text, View, Platform} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';

const GradientBtn = ({label, handleClick, disable, type}) => {
  const activeBtnOpacity = 0.7;
  const btnStyle = [styles.button];
  if (disable) {
    btnStyle.push(styles.disable);
  }

  const gradientColor = colors[type] || colors['blue'];

  return (
    <View>
      <TouchableOpacity onPress={handleClick} activeOpacity={activeBtnOpacity}>
        <LinearGradient
          start={{
            x: 1.0,
            y: 0.0
          }}
          end={{
            x: 0.0,
            y: 1.0
          }}
          colors={gradientColor}
          style={styles.container}>
          <Text style={btnStyle}>{label}</Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );
};

GradientBtn.propTypes = {
  label: PropTypes.string.isRequired,
  handleClick: PropTypes.func.isRequired,
  disable: PropTypes.bool,
  type: PropTypes.string
};

const colors = {
  blue: ['#065af3', '#53b2fe'],
  red: []
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 30,
    justifyContent: 'center'
  },
  button: {
    fontFamily: 'Lato-Bold',
    fontSize: 12,
    fontWeight: 'bold',
    lineHeight: 14,
    letterSpacing: 1,
    textAlign: 'center',
    color: '#ffffff',
    backgroundColor: 'transparent'


  },
  disable: {}
});

export default GradientBtn;
