import React from 'react';
import {Image, TouchableOpacity, StyleSheet, View} from 'react-native';
import PropTypes from 'prop-types';
import closeBtnIcon from '@mmt/legacy-assets/src/close-black.webp';

const CloseBtn = ({handleClose,closeBtnIconUrl}) => {
  const activeBtnOpacity = 0.7;
  return (
    <TouchableOpacity style={styles.iconWrapper}
                      onPress={(e) => { e.preventDefault(); handleClose(); }}
                      activeOpacity={activeBtnOpacity}>
      {closeBtnIconUrl ?<Image source={{uri:closeBtnIconUrl}} style={styles.icon}/> :
       <Image source={closeBtnIcon} style={styles.icon}/>
      }
    </TouchableOpacity>
  );
};

CloseBtn.propTypes = {
  handleClose: PropTypes.func.isRequired
};

const styles = StyleSheet.create({
  icon: {
    width: 15,
    height: 15
  },
  iconWrapper: {
    paddingVertical:15,
    paddingHorizontal: 15
  }
});

export default CloseBtn;
