import React from 'react';
import {TouchableOpacity, Text, StyleSheet} from 'react-native';
import PropTypes from 'prop-types';

const AnchorBtn = ({label, handleClick, disable}) => {
  const activeBtnOpacity = 0.7;
  const btnStyle = [styles.text];
  if (disable) {
    btnStyle.push(styles.disable);
  }

  return (
    <TouchableOpacity style={styles.linkContainer}
                      onPress={handleClick}
                      activeOpacity={activeBtnOpacity}>
      <Text style={btnStyle}>{label}</Text>
    </TouchableOpacity>
  );
};

AnchorBtn.propTypes = {
  label: PropTypes.string.isRequired,
  handleClick: PropTypes.func.isRequired,
  disable: PropTypes.bool
};

const styles = StyleSheet.create({
  linkContainer: {

    paddingLeft: 10
  },
  text: {
    fontFamily: 'Lato',
    fontSize: 12,
    fontWeight: 'bold',
    letterSpacing: 0.23,
    color: '#008cff'
  },
  disable: {
    fontFamily: 'Lato-Bold',
    color: '#b7b7b7'
  }
});