import React from 'react';
import PropTypes from 'prop-types';
import {StyleSheet, Text, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors, fonts} from '../../../Styles/globalStyles';
import getPlatformElevation from '../Card/getPlatformElevation';
import TouchableRipple from '../TouchableRipple';
import {normalisePx} from '../../../Styles/globalStyles';


const CtaButton = ({
  height = normalisePx(40), label, onPress, customTextStyle = null, horizontalPadding = normalisePx(20), buttonGradient = ['#53B2FE', '#065AF3']
}) => (

  <TouchableRipple
    feedbackColor={colors.transparent}
    onPress={() => onPress()}
  >
    <View>
      <LinearGradient
        colors={buttonGradient}
        start={{x: 0.0, y: 0.0}}
        end={{x: 1.0, y: 0.0}}
        style={[styles.cta, {height, borderRadius: (height / 2), paddingHorizontal: horizontalPadding}]}
      >
        <Text style={customTextStyle || styles.text}>{label.toUpperCase()}</Text>
      </LinearGradient>
    </View>
  </TouchableRipple>
);

CtaButton.propTypes = {
  height: PropTypes.number,
  horizontalPadding: PropTypes.number,
  label: PropTypes.string.isRequired,
  onPress: PropTypes.func.isRequired,
  customTextStyle: PropTypes.object,
  buttonGradient: PropTypes.array
};

const styles = ({
  cta: {
    overflow: 'hidden',
    backgroundColor: colors.azure,
    alignItems: 'center',
    justifyContent: 'center',
    ...Platform.select({
      ios: {
        shadowColor: 'rgba(0, 0, 0, 0.12)',
        shadowOpacity: 0.8,
        shadowOffset: { width: 0, height: 2 },
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
      web: {
        shadowColor: 'rgba(0, 0, 0, 0.12)',
        shadowOpacity: 0.8,
        shadowOffset: { width: 0, height: 2 },
        shadowRadius: 4,
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.12)',
      },
    }),
  },
  text: {
    color: colors.white,
    fontSize: 10,
    fontFamily: fonts.black,
    backgroundColor: colors.transparent
  }
});

export default CtaButton;
