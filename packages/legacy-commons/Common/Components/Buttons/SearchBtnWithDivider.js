import React from 'react';
import {View, Text, Image} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import curvedDivider from '@mmt/legacy-assets/src/curved_bg.webp';
import PropTypes from 'prop-types';
import {colors, fonts, normaliseFont} from '../../../Styles/globalStyles';
import getPlatformElevation from '../Card/getPlatformElevation';
import TouchableRipple from '../TouchableRipple';

const searchSize = (72);

const SearchBtnWithDivider = ({onSearchClicked, label, gradientColor = ['#53b2fe', '#065af3']}) => (
  <View style={{
      flexDirection: 'column',
      alignItems: 'center',
      height: searchSize,
      backgroundColor: colors.grayBg
    }}
  >
    <View style={{height: (searchSize / 4), backgroundColor: colors.white, width: '100%'}} />
    <Image
      style={{
        width: '100%',
        height: (searchSize / 2),
        position: 'absolute',
        top: (searchSize / 4),
        backgroundColor: colors.grayBg
      }}
      source={curvedDivider}
      defaultSource={curvedDivider}
    />
    <View
      style={{
        height: searchSize,
        width: searchSize,
        position: 'absolute',
        top: 0,
        borderRadius: (searchSize / 2),
        alignSelf: 'center',
        overflow: 'hidden'
      }}
    >
      <TouchableRipple
        onPress={onSearchClicked}
      >
        <LinearGradient
          colors={gradientColor}
          start={{x: 0.0, y: 0.0}}
          end={{x: 1.0, y: 0.0}}
          style={{
            height: searchSize,
            width: searchSize,
            top: 0,
            borderRadius: (searchSize / 2),
            alignSelf: 'center',
            alignItems: 'center',
            justifyContent: 'center',
            ...getPlatformElevation(8)
          }}
        >
          <Text style={{
            color: colors.white,
            backgroundColor: colors.transparent,
            fontFamily: fonts.bold,
            fontSize: normaliseFont(12)
          }}
          >
            {label}
          </Text>
        </LinearGradient>
      </TouchableRipple>
    </View>
  </View>
);

SearchBtnWithDivider.propTypes = {
  onSearchClicked: PropTypes.func.isRequired,
  label: PropTypes.string
};

SearchBtnWithDivider.defaultProps = {
  label: 'SEARCH',
  gradientColor: ['#53b2fe', '#065af3']
};

export default SearchBtnWithDivider;
