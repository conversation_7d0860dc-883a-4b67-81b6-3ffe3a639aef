import React, {Component} from 'react';
import {StyleSheet, View, TouchableWithoutFeedback, Platform} from 'react-native';
import PropTypes from 'prop-types';
import getPlatformElevation from '../Card/getPlatformElevation';


class TopSheetModal extends Component {
    onTouchOutside = (e) => {
      if (Platform.OS === 'web') {
        e.preventDefault();
      }
      this.props.onTouchOutside();
    };
    render() {
      return (
        <View style={styles.container}>
          {this.props.children}
          <TouchableWithoutFeedback onPress={this.onTouchOutside}>
            <View style={{flex: 1}} />
          </TouchableWithoutFeedback>
        </View>
      );
    }
}

const getElevation = () => {
  if (Platform.OS === 'ios') {
    return getPlatformElevation(100);
  }
  return getPlatformElevation(1000);
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    top: 0,
    right: 0,
    left: 0,
    flex: 1,
    zIndex: 10,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'flex-end',
    ...getElevation()// FIXME send this value as prop
  }
});

TopSheetModal.propTypes = {
  onTouchOutside: PropTypes.func,
  children: PropTypes.element.isRequired
};
TopSheetModal.defaultProps = {
  onTouchOutside: () => {
  }
};

export default TopSheetModal;
