import React, { FC, ReactNode, useEffect } from 'react';

import PropTypes from 'prop-types';
import {
  View,
  Platform,
  StyleSheet,
  TouchableWithoutFeedback,
  StyleProp,
  ViewStyle,
  GestureResponderEvent,
  BackHandler,
} from 'react-native';

import getPlatformElevation from '../Card/getPlatformElevation';
import { colors, statusBarBootomHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

interface Props {
  children: ReactNode;
  onTouchOutside?: () => void;
  hardwareBackButtonClose?: () => void;
  additionalContainerStyle?: StyleProp<ViewStyle>;
  shouldNotShowBlurScreen?: boolean;
  safeAreaViewFooterColor?: string;
}

const { OS } = Platform;

const bottomSheetModalDefaultPropOnTouchOutside = () => {};
const bottomSheetModalDefaultPropAdditionalContainerStyle = {};

const BottomSheetModal: FC<Props> = ({
  children,
  onTouchOutside = bottomSheetModalDefaultPropOnTouchOutside,
  additionalContainerStyle = bottomSheetModalDefaultPropAdditionalContainerStyle,
  hardwareBackButtonClose,
  shouldNotShowBlurScreen,
  safeAreaViewFooterColor = colors.white,
}) => {
  const onPressHandler = (e: GestureResponderEvent) => {
    if (OS === 'web') {
      e.preventDefault();
    }

    onTouchOutside && onTouchOutside();
  };

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (hardwareBackButtonClose) {
        hardwareBackButtonClose();
        return true;
      }

      return false;
    });

    return () => backHandler.remove();
  }, []);

  return (
    <View style={[styles.container, AtomicCss.flex1, additionalContainerStyle]}>
      {!shouldNotShowBlurScreen && (
        <TouchableWithoutFeedback onPress={onPressHandler}>
          <View style={AtomicCss.flex1} />
        </TouchableWithoutFeedback>
      )}

      {children}
      <View style={[styles.footer, { backgroundColor: safeAreaViewFooterColor }]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.7)',
    ...getPlatformElevation(OS === 'ios' ? 100 : 1000), // FIXME send this value as prop
  },
  footer: {
    height: statusBarBootomHeightForIphone,
  },
});

BottomSheetModal.propTypes = {
  onTouchOutside: PropTypes.func,
  children: PropTypes.element.isRequired,
  additionalContainerStyle: PropTypes.object,
};

export default BottomSheetModal;
