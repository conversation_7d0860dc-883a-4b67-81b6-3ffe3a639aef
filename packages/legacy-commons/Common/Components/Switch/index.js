import React from 'react';
import {View, TouchableOpacity} from 'react-native';
import PropTypes from 'prop-types';
import styles from './SwitchCss';

const Switch = ({enabled, onPress}) => {
  const leftStyle = {left: enabled ? 14 : 0};
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={[styles.bottomStrip, enabled ? {} : {backgroundColor: '#c9c9c9'}]} />
      <View style={[styles.upperDot, leftStyle, enabled ? {} : {backgroundColor: '#9b9b9b'}]} />
    </TouchableOpacity>
  );
};


Switch.propTypes = {
  enabled: PropTypes.bool.isRequired,
  onPress: PropTypes.func.isRequired
};

export default Switch;
