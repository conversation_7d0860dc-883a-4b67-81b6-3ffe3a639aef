import React, {Component} from 'react';
import {Image, Text, TouchableOpacity, View} from 'react-native';
import {Actions} from 'react-native-router-flux';
import Card from '../Card';
import styles from './PremiumCommonHeaderCss';

import standardBackIcon from '@mmt/legacy-assets/src/trip_header_back_icon.webp';
import transparentBackIcon from '@mmt/legacy-assets/src/back-white.webp';
import searchIcon from '@mmt/legacy-assets/src/search.webp';
import TouchableRipple from '../TouchableRipple';

class PremiumCommonHeader extends Component {
  renderBackBtn() {
    const backIcon = this.props.transparent ? transparentBackIcon : standardBackIcon;
    return (
      <TouchableOpacity style={styles.backBtn} onPress={this.props.goBack}>
        <Image style={styles.icon} source={backIcon} />
      </TouchableOpacity>
    );
  }
  renderTitle() {
    const textWhiteStyle = this.props.transparent ? 'White' : '';
    const title = this.props.title ? this.props.title : '';
    const boldTitle = this.props.boldTitle ? this.props.boldTitle : '';
    const subTitle = this.props.subTitle ? <Text style={[styles.subTitle, styles[`subTitle${textWhiteStyle}`]]}>{this.props.subTitle}</Text> : false;
    return (
      <View style={{flex: 1}}>
        <TouchableRipple disabled={!this.props.headerClick} onPress={() => (this.props.headerClick ? this.props.headerClick() : console.log('no implementation'))}>
          <View style={styles.headerTitleContent}>
            <Text style={[styles.title, styles[`title${textWhiteStyle}`]]} numberOfLines={1}>{title ? `${title} ` : ''}<Text style={styles.titleThick}>{boldTitle}</Text></Text>
            {subTitle}
          </View>
        </TouchableRipple>
      </View>
    );
  }

  renderSearchBtn() {
    return (
      <TouchableOpacity onPress={() => (this.props.onSearchClick ? this.props.onSearchClick() : console.log('no implementation'))}>
        <Image style={styles.shareIcon} source={searchIcon} />
      </TouchableOpacity>
    );
  }

  render() {
    const headerStyle = this.props.transparent ? styles.transparentHeader : styles.headerCard;
    return (
      <Card style={headerStyle}>
        <View style={styles.content}>
          {this.renderBackBtn()}
          {this.renderTitle()}
          {this.props.onSearchClick && this.renderSearchBtn()}
        </View>
      </Card>
    );
  }
}
export default PremiumCommonHeader;
