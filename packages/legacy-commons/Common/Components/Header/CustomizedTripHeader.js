import React from 'react';
import {Actions} from 'react-native-router-flux';
import PropTypes from 'prop-types';
import {Image, Text, View, StyleSheet, Platform, TouchableOpacity } from 'react-native';
import Card from '../Card';
import {filterAllStationsString} from '../../utils/railsSharableUtils';
import TouchableRipple from '../TouchableRipple';
import styles from './headerStyles';
import {colors, fonts} from '../../../Styles/globalStyles';
import {backIcon} from '../../../Helpers/displayHelper';
import ASSETS from 'apps/rails/src/Utils/Assets/RailsAssets';
import Header from '@Frontend_Ui_Lib_App/Header';

export const SIZE_LARGE = 'large';
export const SIZE_NORMAL = 'normal';
export const SIZE_MEDIUM = 'medium';

export const tripHeaderNormalHeight = 72;
export const tripHeaderLargeHeight = 108;
export const tripHeaderMediumHeight = 88;

const getHeight = (size) => {
  switch (size) {
    case SIZE_LARGE:
      return tripHeaderLargeHeight;
    case SIZE_MEDIUM:
      return tripHeaderMediumHeight;
    case SIZE_NORMAL:
      return tripHeaderNormalHeight;
    default:
      return tripHeaderNormalHeight;
  }
}
const customizedTripHeaderDefaultPropOnPressBackHandler = () => Actions.pop();
const CustomizedTripHeader = ({
  title,
  subTitle = '',
  size = SIZE_NORMAL,
  onPressBackHandler = customizedTripHeaderDefaultPropOnPressBackHandler,
  id,
  cardStyle = {},
  showAvailSubscrnWidget = false,
  isAvailSubscrnEnabled = false,
  displayAvailSubscrnBottomSheet = () => {},
}) => {
  const custStyles = Platform.OS === 'ios' ? custStylesIos : custStylesAndroid;
  const filteredSubTitle = filterAllStationsString(subTitle);
  return (
    <Header
      customStyles={{
        wrapperStyle: [ styles.tripHeader, { height: getHeight(size) } ]
      }}
      rightComponent={
        showAvailSubscrnWidget && (
          <TouchableOpacity
            style={
              isAvailSubscrnEnabled
                ? styles.availSubscrnIconContainerEnabled
                : styles.availSubscrnIconContainerDisabled
            }
            onPress={() => {
              displayAvailSubscrnBottomSheet(true);
            }}
          >
            <Image
              source={ASSETS.blueAvailabilitySubscriptionIcon}
              style={styles.availSubscrnIcon}
            />
            {isAvailSubscrnEnabled && (
              <Image source={ASSETS.greenTick} style={styles.greenTickIcon} />
            )}
          </TouchableOpacity>
        )
      }
      leftComponent={
        <View style={custStyles.container}>
          <TouchableRipple onPress={onPressBackHandler} testID={`${id}_backButton`}>
            <View style={styles.tripHeaderBackIconPadding}>
              <Image style={styles.tripHeaderBackIcon} source={backIcon} />
            </View>
          </TouchableRipple>

          <View style={custStyles.textContainer}>
            <Text style={custStyles.titleText} testID={`${id}_title`}>
              {title}
            </Text>
            <Text style={custStyles.subTitleText} testID={`${id}_subtitle`}>
              {filteredSubTitle}
            </Text>
          </View>
          {Platform.OS === 'ios' && <View style={styles.tripHeaderBackIconPadding} />}
        </View>
      }
    />
  );
};

CustomizedTripHeader.propTypes = {
  title: PropTypes.string.isRequired,
  subTitle: PropTypes.string,
  size: PropTypes.oneOf([SIZE_LARGE, SIZE_NORMAL]),
  onPressBackHandler: PropTypes.func
};

export default CustomizedTripHeader;

const custStylesAndroid = StyleSheet.create({
  container: {
    flexDirection: 'row'
  },
  titleText: {
    fontFamily: fonts.bold,
    fontSize: 18,
    color: colors.defaultTextColor,
    marginTop: 18,
    lineHeight: 22
  },
  subTitleText: {
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.lightTextColor,
    lineHeight: 22

  },
  textContainer: {
    alignSelf: 'flex-start',
    paddingRight: 20,
  },
  image: {width: 24, height: 24}
});

const custStylesIos = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1
  },

  titleText: {
    fontFamily: fonts.bold,
    fontSize: 17,
    color: colors.defaultTextColor,
    marginTop: 18,
    lineHeight: 22
  },
  subTitleText: {
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.lightTextColor,
    lineHeight: 22

  },
  textContainer: {
    alignItems: 'center',
    flexDirection: 'column'
  },
  image: {width: 24, height: 24}
});
