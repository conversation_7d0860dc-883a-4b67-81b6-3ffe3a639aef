import React from 'react';
import { Actions } from 'react-native-router-flux';
import PropTypes from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import { Image, Text, View, StyleSheet, Button } from 'react-native';
import Card from '../Card';
import styles from './headerStyles';
import { colors, fonts } from '../../../Styles/globalStyles';
import TouchableOpacity from '../TouchableOpacity';
import { TRIP_HEADER_TEST_ID } from './TripHeaderTestIds';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { getDayByDate, getMonthByDate } from '../Vernacular/calendarUtil';

export const SIZE_LARGE = 'large';
export const SIZE_NORMAL = 'normal';
// eslint-disable-next-line import/no-unresolved
const backIcon = require('@mmt/legacy-assets/src/ic_back_arrow.webp');
const edit_icon = require('@mmt/legacy-assets/src/group_16.webp');

export const tripHeaderNormalHeight = 60;
export const tripHeaderLargeHeight = 108;

const tripHeaderWithEditOptionDefaultPropBackHandler = () => Actions.pop();
const tripHeaderWithEditOptionDefaultPropOnPressBackHandler = () => Actions.pop();
const tripHeaderWithEditOptionDefaultPropOnPressEditHandler = () => Actions.pop();
const tripHeaderWithEditOptionDefaultPropLobStyles = {};

const TripHeaderWithEditOption = ({
  title,
  subTitle = '',
  size = SIZE_NORMAL,
  onPressBackHandler = tripHeaderWithEditOptionDefaultPropOnPressBackHandler,
  onPressEditHandler = tripHeaderWithEditOptionDefaultPropOnPressEditHandler,
  closeStationChangeAlert,
  postPaymentBooking = undefined,
  onBack,
  showHideEditWidget,
  showEditIcon,
  skipUniversalWidgetParam,
  style,
  listing_edit,
  lobStyles = tripHeaderWithEditOptionDefaultPropLobStyles,
  editIcon = edit_icon,
}) => {
  const editClicked = {
    isEditClicked: true,
    showEditWidget: true,
  };

  function backHandler() {
    // skipUniversalWidgetParam();
    if (onBack) {
      onBack();
      return;
    }
    closeStationChangeAlert && closeStationChangeAlert();
    onPressBackHandler && onPressBackHandler(postPaymentBooking);
  }

  return (
    <Card
      style={StyleSheet.flatten([custStyles.cardStyle, lobStyles?.cardStyle])}
      elevation={0}
      noBorder
    >
      <View style={[custStyles.viewContainer, lobStyles?.viewContainer]}>
        <View style={[custStyles.flexRow, custStyles.flex1]}>
          <TouchableOpacity onPress={backHandler}>
            <View style={[{ ...styles.tripHeaderBackIconPadding, marginTop: 0, }, lobStyles?.tripHeaderBackIconPadding]} testID={TRIP_HEADER_TEST_ID.BACK_ICON}>
              <Image style={[styles.tripHeaderBackIcon, lobStyles?.tripHeaderBackIcon]} source={backIcon} />
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={custStyles.flex1} onPress={showHideEditWidget.bind(this, editClicked)}>
            <View style={[custStyles.flex1, AtomicCss.justifyCenter]}>
              <Text style={[custStyles.titleText, lobStyles?.titleText]} ellipsizeMode="tail" numberOfLines={1} testID={TRIP_HEADER_TEST_ID.TITLE}>
                {title}
              </Text>
              <Text style={[custStyles.subTitleText, lobStyles?.subTitleText]} testID={TRIP_HEADER_TEST_ID.SUBTITLE}>{subTitle}</Text>
            </View>
          </TouchableOpacity>
        </View>
        {showEditIcon && (
          <TouchableOpacity
            style={custStyles.selfCenter}
            onPress={showHideEditWidget.bind(this, editClicked)}
          >
            <View style={[custStyles.editBtnStyle, lobStyles?.editBtnStyle]} testID={TRIP_HEADER_TEST_ID.EDIT_BUTTON}>
              <Image style={custStyles.tripHeaderEditIcon} source={editIcon} />
              {listing_edit && <Text style={custStyles.listingEdit}>{listing_edit}</Text>}
            </View>
          </TouchableOpacity>
        )}
      </View>
    </Card>
  );
};

TripHeaderWithEditOption.propTypes = {
  title: PropTypes.string.isRequired,
  subTitle: PropTypes.string,
  size: PropTypes.oneOf([SIZE_LARGE, SIZE_NORMAL]),
  backHandler: PropTypes.func,
  onPressBackHandler: PropTypes.func,
  onPressEditHandler: PropTypes.func,
  postPaymentBooking: PropTypes.bool,
  closeStationChangeAlert: PropTypes.func,
  lobStyles: PropTypes.object,
};

export default TripHeaderWithEditOption;

const custStyles = StyleSheet.create({
  flex1: {
    flex: 1
  },
  flexRow: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  selfCenter: {
    alignSelf: 'center'
  },
  listingEdit: { 
    color: '#008cff', 
    fontSize: 11, 
    fontFamily: fonts.bold 
  },
  titleText: {
    fontFamily: fonts.black,
    fontSize: 14,
    color: colors.defaultTextColor,
    paddingTop: 5,
    lineHeight: 22,
    maxWidth: 300,
    fontWeight: "700"
  },
  subTitleText: {
    fontFamily: fonts.regular,
    fontSize: 11,
    color: colors.lightTextColor,
    lineHeight: 18,
  },
  image: { width: 24, height: 24 },
  editBtnStyle: {
    height: 35,
    display: 'flex',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    borderRadius: 10,
    marginHorizontal: 20,
  },
  tripHeaderEditIcon: {
    height: 16,
    width: 16,
  },
  cardStyle: {
    height: 60,
    flexDirection: 'row',
    marginVertical: 5,
    marginHorizontal: 0,
    justifyContent: 'flex-start',
    zIndex: 10,
    elevation: 0,
  },
  viewContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    marginHorizontal: 8,
    marginVertical: 4,
    borderColor: colors.grey12,
    backgroundColor: colors.grey13,
    borderRadius: 2,
    borderWidth: 1,
  },
});
