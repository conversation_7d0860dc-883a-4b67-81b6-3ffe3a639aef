import React from 'react';
import PropTypes from 'prop-types';
import {Image, Text, View} from 'react-native';
import {Actions} from 'react-native-router-flux';
import Card from '../Card';
import {colors, fonts} from '../../../Styles/globalStyles';
import TouchableRipple from '../TouchableRipple';
import { getImageTransformStyling } from 'apps/post-sales/src/utils/PostSaleUtil';

// eslint-disable-next-line import/no-unresolved
const backIcon = require('@mmt/legacy-assets/src/ic_back_arrow.webp');

const simpleHeaderDefaultPropIconPress = () => Actions.pop();
const simpleHeaderDefaultPropRightIconPress = () => {};

const SimpleHeader = ({
  icon = backIcon,
  title,
  iconPress = simpleHeaderDefaultPropIconPress,
  elevation = 4,
  textFont = fonts.medium,
  textColor = colors.defaultTextColor,
  textSize = 18,
  rightIcon = null,
  rightIconPress = simpleHeaderDefaultPropRightIconPress,
  subtitle = defaultSubtitle,
  extraBottom = 0,
  rightChild,
  childLeftToRightIcon,
}) => {
  const subtitleProp = {...defaultSubtitle, ...subtitle};
  return (
    <Card
      style={{marginHorizontal: 0, marginVertical: 0, paddingBottom: extraBottom}}
      elevation={elevation}
      noBorder
    >
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        height: 56
      }}
      >
        <TouchableRipple onPress={iconPress}>
          <View style={{padding: 16}}>
            <Image source={icon} style={[getImageTransformStyling(), {width: 16, height: 16, resizeMode: 'center'}]} />
          </View>
        </TouchableRipple>
        <View style={{
          flex: 1, flexDirection: 'column', marginRight: rightIcon ? 0 : 48
        }}
        >
          <Text style={{
            fontSize: textSize, fontFamily: textFont, color: textColor, textAlign: 'left'
          }}
          >
            {title}
          </Text>
          {!!subtitleProp.text &&
            <Text style={{
              fontSize: subtitleProp.size, fontFamily: subtitleProp.font, color: subtitleProp.color, textAlign: 'left'
            }}>
              {subtitleProp.text}
            </Text>
          }
        </View>
        { childLeftToRightIcon }
        {
          rightIcon &&
          <TouchableRipple onPress={rightIconPress}>
            <View style={{
              width: 48, height: 48, flex: 0, justifyContent: 'center', alignItems: 'center'
            }}
            >
              <Image style={{width: 16, height: 18, resizeMode: 'contain'}} source={rightIcon} />
            </View>
          </TouchableRipple>
        }
        {rightChild}
      </View>
    </Card>
  );
};

SimpleHeader.propTypes = {
  icon: Image.propTypes.source,
  rightIcon: Image.propTypes.source,
  elevation: PropTypes.number,
  title: PropTypes.string.isRequired,
  iconPress: PropTypes.func,
  rightIconPress: PropTypes.func,
  textFont: PropTypes.string,
  textColor: PropTypes.string,
  textSize: PropTypes.number,
  extraBottom: PropTypes.number,
  subtitle: PropTypes.object,
  rightChild:PropTypes.elementType,
};

const defaultSubtitle = {
  text: null,
  font: fonts.regular,
  color: colors.lightTextColor,
  size: 12
};

export default SimpleHeader;
