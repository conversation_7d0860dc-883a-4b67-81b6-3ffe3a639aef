import React from 'react';
import {Actions} from 'react-native-router-flux';
import PropTypes from 'prop-types';
import {Image, StyleSheet, Text, View, Platform} from 'react-native';
import Card from '../Card';
import TouchableRipple from '../TouchableRipple';
import {colors, fonts} from '../../../Styles/globalStyles';
import Header from '@Frontend_Ui_Lib_App/Header';
import getPlatformElevation from '../Card/getPlatformElevation';

export const SIZE_LARGE = 'large';
export const SIZE_NORMAL = 'normal';
// eslint-disable-next-line import/no-unresolved
const closeIcon = require('@mmt/legacy-assets/src/ic-headerclose-grey.webp');

export const tripHeaderNormalHeight = 56;
export const tripHeaderLargeHeight = 108;

const SimpleHeaderForPopupAndroid = ({ subTitle, title, onPressCloseHandler, size }) => (
  <Header
    customStyles={{ wrapperStyle: [stylesIos.headerWrapperStyle, {   
       height: size === SIZE_LARGE ? tripHeaderLargeHeight : tripHeaderNormalHeight,
    }] }}
    leftIcon={{
      icon: closeIcon,
      customStyles: {
        iconContainerStyle: stylesIos.closeIconStyle,
      },
      onPress: onPressCloseHandler,
    }}
    leftComponent={
      <View style={{ marginLeft: 20, flex: 1 }}>
        <Text style={stylesIos.titleStyle}>{title}</Text>
        {subTitle && <Text style={stylesIos.subTitleStyle}>{subTitle}</Text>}
      </View>
    }
  />
);

SimpleHeaderForPopupAndroid.propTypes = {
  title: PropTypes.string.isRequired,
  size: PropTypes.oneOf([SIZE_LARGE, SIZE_NORMAL]),
  onPressCloseHandler: PropTypes.func
};

SimpleHeaderForPopupAndroid.defaultProps = {
  size: SIZE_NORMAL,
  onPressCloseHandler: () => Actions.pop()
};

const stylesAndroid = StyleSheet.create({

  titleTextStyle: {
    fontSize: 18,
    fontFamily: fonts.bold,
    color: colors.black04
  },
  closeIconStyle: {
    width: 24,
    height: 24
  },
  subTitleStyle: {
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
    fontSize: 12
  }
});

const SimpleHeaderForPopupIos = ({ subTitle, title, onPressCloseHandler, size }) => (
  <Header
    customStyles={{ wrapperStyle: [stylesIos.headerWrapperStyle, { height: size === SIZE_LARGE ? tripHeaderLargeHeight : tripHeaderNormalHeight,}] }}
    leftIcon={{
      icon: closeIcon,
      customStyles: {
        iconContainerStyle: stylesIos.closeIconStyle,
      },
      onPress: onPressCloseHandler,
    }}
    leftComponent={
      <View style={{ marginLeft: 20, flex: 1 }}>
        <Text style={stylesIos.titleStyle}>{title}</Text>
        {subTitle && <Text style={stylesIos.subTitleStyle}>{subTitle}</Text>}
      </View>
    }
  />
);

SimpleHeaderForPopupIos.propTypes = {
  title: PropTypes.string.isRequired,
  size: PropTypes.oneOf([SIZE_LARGE, SIZE_NORMAL]),
  onPressCloseHandler: PropTypes.func,
  subTitle: PropTypes.string
};

SimpleHeaderForPopupIos.defaultProps = {
  size: SIZE_NORMAL,
  onPressCloseHandler: () => Actions.pop(),
  subTitle: null
};

const stylesIos = StyleSheet.create({
  titleStyle: {
    fontSize: 18,
    fontFamily: fonts.bold,
    color: colors.black04,
    letterSpacing: 0
  },
  closeIconStyle: {
    width: 24,
    height: 24
  },
  subTitleStyle: {
    fontFamily: fonts.regular,
    color: colors.defaultTextColor,
    fontSize: 12
  },
  headerWrapperStyle: {
    marginHorizontal: 0,
    marginVertical: 0,
    paddingVertical: 0,
    ...getPlatformElevation(5),
    flexDirection: 'row',
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  }
});


export default Platform.OS === 'ios' ? SimpleHeaderForPopupIos : SimpleHeaderForPopupAndroid;
