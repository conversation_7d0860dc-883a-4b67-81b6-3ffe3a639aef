import {Platform} from 'react-native';
import getPlatformElevation from '../Card/getPlatformElevation';
import {colors, fonts} from '../../../Styles/globalStyles';


const styles = {
  container: {
    flex: 0,
    flexDirection: 'row',
    height: 56,
    padding: 0,
    marginVertical: 0,
    marginHorizontal: 0,
    ...getPlatformElevation(Platform.select({ios: 1.5, android: 2}))
  },
  headerCard: {
    flex: 0,
    flexDirection: 'row',
    height: 96,
    padding: 0,
    marginVertical: 0,
    marginHorizontal: 0,
    ...getPlatformElevation(Platform.select({ios: 1.5, android: 2}))
  },
  content: {
    flexDirection: 'row',
    height: 96,
    alignItems: 'center'
  },
  iconPadding: {
    width: 48,
    height: 48
  },
  icon: {
    width: 15,
    height: 15,
    margin: 16
  },
  tripHeader: {
    flexDirection: 'row',
    marginVertical: 0,
    marginHorizontal: 0,
    justifyContent: 'flex-start',
    elevation: 0,
    paddingVertical: 0,
    paddingHorizontal: 0,
    borderBottomWidth: 0.5, 
    borderColor: colors.lightGrey,
  },
  tripHeaderBackIconPadding: {
    width: 48,
    height: 48,
    marginTop: 8,
    alignItems: 'center',
    justifyContent: 'center'
  },
  tripHeaderBackIcon: {
    width: 16,
    height: 16
  },
  tripHeaderEditIconPadding: {
    width: 48,
    height: 48,
    position:'absolute',
    right:0,
    justifyContent: 'center',
    marginTop: 16,

    // alignItems: 'flex-end',
    // justifyContent: 'center',
  },
  tripHeaderEditIcon: {
    // width: 16,
    // height: 16
    position: 'absolute',
    right: 0
  },
  title: {
    color: colors.black,
    fontSize: 22,
    fontFamily: fonts.light
  },
  tripHeaderTitle: {
    color: colors.black,
    fontSize: 18,
    fontFamily: fonts.bold,
    marginTop: 18,
    lineHeight: 22,
    maxWidth: 300
  },
  titleThick: {
    fontFamily: fonts.bold
  },
  tripHeaderContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.white,
    padding: 8
  },
  tripHeaderSubTitle: {
    fontSize: 14,
    fontFamily: fonts.regular,
    marginTop: 3,
    color: colors.lightTextColor,
    lineHeight: 17,
    maxWidth: 300
  },
  availSubscrnIconContainerEnabled: {
    marginLeft: 'auto',
    marginRight: 16,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    height: 40,
    width: 40,
    backgroundColor: colors.lighterBlue,
    borderRadius: 10,
    marginTop: 5,
  },
  availSubscrnIconContainerDisabled: {
    marginLeft: 'auto',
    marginRight: 16,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    height: 40,
    width: 40,
    marginTop: 5,
  },
  availSubscrnIcon: {
    width: 24,
    height: 24,
  },
  greenTickIcon: {
    position: 'absolute',
    width: 13,
    height: 13,
    top: 5,
    right: 5,
  },
};

export default styles;
