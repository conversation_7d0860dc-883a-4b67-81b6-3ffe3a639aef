import React from 'react';
import { Actions } from 'react-native-router-flux';
import _isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';
import { Image, Text, View, StyleSheet, TouchableOpacity } from 'react-native';
import Card from '../Card';
import styles from './headerStyles';
import TouchableRipple from '../TouchableRipple';
import { backIcon } from '../../../Helpers/displayHelper';
import { colors, fonts } from '../../../Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

export const SIZE_LARGE = 'large';
export const SIZE_NORMAL = 'normal';
// eslint-disable-next-line import/no-unresolved
// const editIcon = require('src/Assets/ic-edit-stroke.webp');
const edit_icon = require('@mmt/legacy-assets/src/group_16.webp');

export const tripHeaderNormalHeight = 72;
export const tripHeaderLargeHeight = 108;

const headerFileDefaultPropBackHandler = () => Actions.pop();
const headerFileDefaultPropOnPressBackHandler = () => Actions.pop();
const headerFileDefaultPropOnPressEditHandler = () => Actions.pop();
const headerFileDefaultPropLobStyles = {};

const HeaderFile = ({
  title,
  subTitle = '',
  size = SIZE_NORMAL,
  onPressBackHandler = headerFileDefaultPropOnPressBackHandler,
  onPressEditHandler = headerFileDefaultPropOnPressEditHandler,
  postPaymentBooking = undefined,
  page,
  onBack,
  showHideEditWidget,
  skipUniversalWidgetParam,
  showEditIcon = true,
  listing_edit,
  lobStyles = headerFileDefaultPropLobStyles,
  editIcon = edit_icon,
  removeTrackOfViewedProp54OmnitureEventsFromSession,
}) => {
  const elevation = !_isEmpty(page) && page === 'newListing' ? 1 : 2;
  const borderBottomWidth = !_isEmpty(page) && page === 'newListing' ? 1 : 0;
  const editClicked = {
    isEditClicked: true,
    showEditWidget: true,
  };

  function backHandler() {
    // skipUniversalWidgetParam();
    if (onBack) {
      onBack();
      if (typeof removeTrackOfViewedProp54OmnitureEventsFromSession === 'function') {
        removeTrackOfViewedProp54OmnitureEventsFromSession?.();
      }
      return;
    } else if (onPressBackHandler) {
      onPressBackHandler();
      if (typeof removeTrackOfViewedProp54OmnitureEventsFromSession === 'function') {
        removeTrackOfViewedProp54OmnitureEventsFromSession?.();
      }
      return;
    }
    onPressEditHandler && onPressEditHandler(postPaymentBooking);
  }

  return (
    <Card
      style={StyleSheet.flatten([{
        ...custStyles.cardStyle,
        borderBottomWidth: StyleSheet.hairlineWidth * borderBottomWidth,
      }, lobStyles?.cardStyle])}
      elevation={elevation}
    >
      <View style={[custStyles.viewContainer, lobStyles?.viewContainer]}>
        <View style={[custStyles.flexRow, custStyles.flex1]}>
          <TouchableRipple onPress={backHandler}>
            <View style={[{ ...styles.tripHeaderBackIconPadding, marginTop: 0, }, lobStyles?.tripHeaderBackIconPadding]}>
              <Image style={[styles.tripHeaderBackIcon, lobStyles?.tripHeaderBackIcon]} source={backIcon} />
            </View>
          </TouchableRipple>
          <TouchableOpacity style={custStyles.flex1} onPress={showHideEditWidget.bind(this, editClicked)}>
            <View style={[custStyles.flex1, AtomicCss.justifyCenter]}>
              <Text style={[custStyles.titleText, lobStyles?.titleText]} ellipsizeMode="tail" numberOfLines={1}>
                {title}
              </Text>
              <Text style={[custStyles.subTitleText, lobStyles?.subTitleText]}>{subTitle}</Text>
            </View>
          </TouchableOpacity>
        </View>
        {showEditIcon && (
          <TouchableRipple
            style={[custStyles.selfCenter, custStyles.editButtonContainer]}
            onPress={showHideEditWidget.bind(this, editClicked)}
          >
            <View style={[custStyles.editBtnStyle, lobStyles?.editBtnStyle]}>
              <Image style={custStyles.tripHeaderEditIcon} source={editIcon} />
              {listing_edit && <Text style={custStyles.edit}>{listing_edit}</Text>}
            </View>
          </TouchableRipple>
        )}
      </View>
    </Card>
  );
};

HeaderFile.propTypes = {
  title: PropTypes.string.isRequired,
  subTitle: PropTypes.string,
  size: PropTypes.oneOf([SIZE_LARGE, SIZE_NORMAL]),
  backHandler: PropTypes.func,
  onPressBackHandler: PropTypes.func,
  onPressEditHandler: PropTypes.func,
  postPaymentBooking: PropTypes.bool,
  showEditIcon: PropTypes.bool,
  lobStyles: PropTypes.object,
};

export default HeaderFile;

const custStyles = StyleSheet.create({
  flex1: {
    flex: 1
  },
  flexRow: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  selfCenter: {
    alignSelf: 'center'
  },
  titleText: {
    fontFamily: fonts.black,
    fontSize: 16,
    color: colors.defaultTextColor,
    paddingTop: 5,
    maxWidth: 300,
  },
  subTitleText: {
    fontFamily: fonts.regular,
    fontSize: 12,
    color: colors.lightTextColor,
    lineHeight: 18,
  },
  image: { width: 24, height: 24 },
  editBtnStyle: {
    height: 40,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    marginHorizontal: 10,
    marginVertical: 10,
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  tripHeaderEditIcon: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
  cardStyle: {
    height: 64,
    marginBottom: 10,
    marginHorizontal: 0,
    shadowColor: 'rgba(0,0,0, 0.0)',
    shadowOffset: { height: 0, width: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    zIndex: 10,
  },
  viewContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 15,
    borderColor: colors.grey12,
    backgroundColor: colors.grey13,
    borderRadius: 2,
    borderWidth: 1,
  },
  edit:{
    color: '#008cff',
    fontSize: 11,
    fontFamily: fonts.bold
  },
  editButtonContainer: {
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  }
});
