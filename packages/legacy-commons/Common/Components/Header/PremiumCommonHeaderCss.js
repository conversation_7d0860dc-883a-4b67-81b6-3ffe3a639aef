import getPlatformElevation from '../Card/getPlatformElevation';
import {colors, fonts} from '../../../Styles/globalStyles';

const styles = {
  headerCard: {
    flexDirection: 'row',
    padding: 0,
    marginVertical: 0,
    marginHorizontal: 0,
    ...getPlatformElevation(2)
  },
  transparentHeader: {
    flexDirection: 'row',
    padding: 0,
    marginVertical: 0,
    marginHorizontal: 0,
    backgroundColor: 'transparent',
    elevation: 0
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    paddingVertical: 13
  },
  headerTitleContent: {
    flexDirection: 'column'
  },
  backBtn: {
    justifyContent: 'center',
    height: 32
  },
  icon: {
    width: 15,
    height: 15,
    marginHorizontal: 16
  },
  shareIcon: {
    width: 18,
    height: 18,
    marginLeft: 5,
    marginRight: 15
  },
  title: {
    color: colors.black,
    fontSize: 14,
    fontFamily: fonts.light
  },
  titleWhite: {
    color: colors.white
  },
  titleThick: {
    fontFamily: fonts.bold
  },
  subTitle: {
    color: colors.lightTextColor,
    fontSize: 11,
    lineHeight: 16,
    marginTop: 2
  },
  subTitleWhite: {
    color: colors.white
  }
};

export default styles;
