

import React from 'react';
import {Image, Platform, Text, View} from 'react-native';
import {Actions} from 'react-native-router-flux';
import PropTypes from 'prop-types';
import Card from '../Card';
import TouchableRipple from '../TouchableRipple';
import getPlatformElevation from '../Card/getPlatformElevation';
import {colors, fonts} from '../../../Styles/globalStyles';
import {backIcon} from '../../../Helpers/displayHelper';
import { fontStyle } from '@mmt/legacy-commons/Common/Components/Vernacular/Utils';

const HEADER_HEIGHT = 56;

const CosmosPageHeader = ({
  title, boldTitle, onIconClick, extraPadding = 0, showBottomBorder, headerHeight = HEADER_HEIGHT, backgroundBackButton
}) => (
  <Card
    style={{
      ...styles.container,
      height: (headerHeight + extraPadding),
      ...getPlatformElevation(0)
    }}
    elevation={1}
    noBorder={!showBottomBorder}
  >
    <View style={styles.content}>
      <TouchableRipple onPress={onIconClick} feedbackColor={backgroundBackButton}>
        <View style={styles.iconPadding}>
          <Image style={styles.icon} source={backIcon}/>
        </View>
      </TouchableRipple>
      <View style={styles.body}>
        <Text style={[styles.title, fontStyle('light')]}>
          {title}<Text style={[styles.titleThick, fontStyle('bold')]}>{` ${boldTitle} `}</Text>
        </Text>
      </View>
    </View>
  </Card>
);


CosmosPageHeader.propTypes = {
  title: PropTypes.string.isRequired,
  boldTitle: PropTypes.string.isRequired,
  onIconClick: PropTypes.func,
  extraPadding: PropTypes.number,
  showBottomBorder: PropTypes.bool,
  headerHeight: PropTypes.number
};

CosmosPageHeader.defaultProps = {
  extraPadding: 0,
  headerHeight: HEADER_HEIGHT,
  onIconClick: () => {
    Actions.pop();
  },
  showBottomBorder: true
};

const styles = {
  container: {
    flexDirection: 'row',
    height: HEADER_HEIGHT,
    padding: 0,
    marginVertical: 0,
    marginHorizontal: 0,
    ...getPlatformElevation(Platform.select({ios: 1.5, android: 2}))
  },
  content: {
    flex: 1,
    flexDirection: 'row',
    height: HEADER_HEIGHT
  },
  iconPadding: {
    width: 48,
    height: 48,
    flexDirection: 'column',
    alignSelf: 'flex-start',
    justifyContent: 'center',
    alignItems: 'center'
  },
  icon: {
    width: 16,
    height: 16
  },
  body: {
    justifyContent: 'center',
    height: 48
  },
  title: {
    color: colors.black,
    padding: 0,
    alignSelf: 'center',
    alignItems: 'center',
    fontFamily: fonts.light,
    textAlign: Platform.select({
      ios: 'center',
      android: 'left'
    }),
    fontSize: 18
  },
  titleThick: {
    fontFamily: fonts.bold,
  }
};

export default CosmosPageHeader;
