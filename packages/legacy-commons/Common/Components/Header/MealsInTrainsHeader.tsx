import LinearGradient from 'react-native-linear-gradient';
import React from 'react';
import {
  Image,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  I18nManager,
} from 'react-native';
import { colors } from '../../../Styles/globalStyles';

interface MealsInTrainsHeaderProps {
  headerText: string;
  imgSrc: any;
  backPressHandler: () => void;
  whiteHeader?: boolean;
}

const imageTransformY = { transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] };

const MealsInTrainsHeader: React.FC<MealsInTrainsHeaderProps> = ({
  headerText,
  imgSrc,
  backPressHandler,
}) => {
  return (
    <LinearGradient
      start={{ x: 1.0, y: 0.0 }}
      end={{ x: 0.0, y: 1.0 }}
      colors={['#065AF3','#53B2FE']}
      style={styles.topHeader}
    >
      <View style={styles.stickyBarWrapper}>
        <TouchableOpacity style={{ padding: 14 }} onPress={backPressHandler}>
          <Image style={[styles.backarrow, imageTransformY]} source={imgSrc} />
        </TouchableOpacity>
        <View>
          <Text numberOfLines={1} ellipsizeMode="tail" style={styles.title}>
            {headerText}
          </Text>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  title: {
    color: colors.white,
    marginBottom: 3,
    paddingLeft: 14,
    ...Platform.select({
      ios: {
        backgroundColor: 'transparent',
      },
    }),
  },
  backarrow: {
    width: 16,
    height: 16,
  },
  topHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 56,
  },
  stickyBarWrapper: {
    flexDirection: 'row',
    width: '75%',
    alignItems: 'center',
  },
});

export default MealsInTrainsHeader;
