import { addressFieldError, comapanyNameFieldError, stateFieldError } from './Store/action';
import {
  COMPANY_ADDRESS_ERROR_MESSAGE,
  COMPANY_NAME_ERROR_MESSAGE,
  STATE_ERROR_MESSAGE,
} from './Helpers/constants';

const corpGstinSubmit = (corpGstinReducer: any, dispatch: any) => {
  const { state, gstNumber, companyName, companyAddress, saveGstDetails } = corpGstinReducer;

  const response = {
    state,
    gstNumber,
    companyName,
    address: companyAddress,
    saveGstDetails,
    error: false,
    errorMessage: '',
  };

  // validate error
  if (state === '') {
    dispatch(stateFieldError({ stateError: true }));

    return {
      ...response,
      error: true,
      errorMessage: STATE_ERROR_MESSAGE,
    };
  }

  if (Boolean(gstNumber)) {
    if (companyName === '') {
      dispatch(comapanyNameFieldError({ companyNameError: true }));

      return {
        ...response,
        error: true,
        errorMessage: COMPANY_NAME_ERROR_MESSAGE,
      };
    }

    if (companyAddress === '') {
      dispatch(addressFieldError({ addressError: true }));

      return {
        ...response,
        error: true,
        errorMessage: COMPANY_ADDRESS_ERROR_MESSAGE,
      };
    }
  }

  return response;
};

export default corpGstinSubmit;
