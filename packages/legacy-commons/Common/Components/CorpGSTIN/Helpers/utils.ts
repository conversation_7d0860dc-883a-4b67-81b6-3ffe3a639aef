import {
  checkForLocationPermission,
  getCurrentLatLng,
  // @ts-ignore
} from '@mmt/legacy-commons/Helpers/locationHelper';

import { StateList } from './interface';
import { Method, StatesData } from './constants';

const LOCUS_API = 'https://mapi.makemytrip.com/locus/web/v2/graphQl';
const STATE_LIST_API = 'https://connect.makemytrip.com/mobile-core-api-web/api/config';

const headers = {
  'Content-Type': 'application/json',
  'api-key': 'GROWTH-MMTGROWTH-71815232081313207181523208',
};

const getRequestBody = (lat: string, lng: string) => `{
    locationHierarchy(lat: ${lat},lng: ${lng}) {
        state {
            name
        }
    }
}`;

export const getSelectedState = async () => {
  let selectedState = '';
  try {
    const hasPermission = await checkForLocationPermission();

    if (!hasPermission) {
      return selectedState;
    }

    const location = await getCurrentLatLng();
    const requestBody = getRequestBody(location.lat, location.lng);

    const response = await fetch(LOCUS_API, {
      method: Method.POST,
      headers: headers,
      body: requestBody,
    });

    const selectedStateResponse = await response.json();

    const { data, success } = selectedStateResponse ?? {};

    if (!success) {
      return selectedState;
    }

    selectedState = data?.locationHierarchy?.state?.[0]?.name;

    return selectedState;
  } catch (e) {
    return selectedState;
  }
};

const sortStateListByName = (stateList: StateList[]) => {
  const sortedStateList = stateList.sort((currStateObj, nextStateObj) => {
    const currState = currStateObj?.name?.toLowerCase();
    const nextState = nextStateObj?.name?.toLowerCase();

    if (currState < nextState) {
      return -1;
    }
    if (currState > nextState) {
      return 1;
    }

    return 0;
  });

  return sortedStateList;
};

export const getStateList = async () => {
  const requestBody = JSON.stringify({
    requestMap: {
      MMT_STATE_LIST: {},
    },
  });

  try {
    const response = await fetch(STATE_LIST_API, {
      headers: { 'Content-Type': 'application/json' },
      method: Method.POST,
      body: requestBody,
    });

    const stateResponse = await response.json();
    const stateList = stateResponse?.response?.MMT_STATE_LIST?.data;

    if (stateList?.length > 0) {
      return sortStateListByName(stateList);
    }

    return StatesData;
  } catch (e) {
    return StatesData;
  }
};

export const isInStateList = (selectedState: string) => (state: StateList) =>
  state.name === selectedState;
