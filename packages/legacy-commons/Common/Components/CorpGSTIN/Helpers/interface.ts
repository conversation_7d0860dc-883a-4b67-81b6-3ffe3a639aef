export interface StateSelectionProps {
  closeHandler: () => void;
  selectHandler: (val: string) => void;
  stateList: StateList[];
}

export type BillingAddressProps = {
  state: string;
  gst: string;
  comapany: string;
  address: string;
  saveGstDetails: boolean;
  onStateChange: (arg: boolean) => void;
  onGstChange: (arg: string) => void;
  onCompanyChange: (arg: string) => void;
  onAddressChange: (arg: string) => void;
  onSaveDetails: (arg: boolean) => void;
  errorState: {
    stateError: boolean;
    companyNameError: boolean;
    addressError: boolean;
  };
};

export type InputFieldProps = {
  ref: any;
  multiline?: boolean;
  label: string;
  value: string;
  onBlur: () => void;
  onFocus: () => void;
  onChange: (arg: string) => void;
  placeholder: string;
  customStyles: any;
  labelStyles: any;
  isError: boolean;
  errorMessage: string;
  textAlignVertical?: 'auto' | 'top' | 'bottom' | 'center';
};

export type StateProps = {
  state: string;
  gstNumber: string;
  companyName: string;
  companyAddress: string;
  saveGstDetails: boolean;
  errorState: {
    stateError: boolean;
    companyNameError: boolean;
    addressError: boolean;
  };
};

export type DispatchProps = {
  initialise: () => void;
  stateUpdate: (state: string) => void;
  gstNumberUpdate: (gstNumber: string) => void;
  companyNameUpdate: (companyName: string) => void;
  addressUpdate: (address: string) => void;
  saveGstDetailToggle: (shoudSaveGstDetail: boolean) => void;
  resetComapanyNameFieldErr: () => void;
  resetAddressFieldErr: () => void;
};

export type SearchBarProps = {
  value: string;
  onChange: (e: any) => void;
  onClose: () => void;
  placeholder: string;
  textInputPlaceholder?: string;
};

export interface Error {
  stateError: boolean;
  companyNameError: boolean;
  addressError: boolean;
}

export type StateList = {
  name: string;
  id: string;
};
