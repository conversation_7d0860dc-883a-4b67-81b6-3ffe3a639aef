import { Error } from '../Helpers/interface';
import {
  INITIALISE_CORP_GSTIN,
  STATE_UPDATE,
  GST_NUMBER_UPDATE,
  ADDRESS_UPDATE,
  COMPANY_NAME_UPDATE,
  SAVE_GST_DETAIL,
  STATE_ERROR,
  COMPANY_NAME_ERROR,
  ADDRESS_ERROR,
} from './action';


export type StateType = {
  state: string;
  gstNumber: string | null;
  companyName?: string;
  companyAddress: string;
  saveGstDetails: boolean;
  errorState: Error;
};

type ActionType = {
  type: string;
  payload: string | Partial<Error>;
};

const initialState = {
  state: '',
  gstNumber: null,
  companyName: '',
  companyAddress: '',
  saveGstDetails: false,
  errorState: {
    stateError: false,
    companyNameError: false,
    addressError: false,
  },
};

const reducer = (state: StateType = initialState, action: ActionType) => {
  switch (action.type) {
    case INITIALISE_CORP_GSTIN:
      return { ...initialState };

    case STATE_UPDATE:
      return {
        ...state,
        state: action.payload,
      };

    case GST_NUMBER_UPDATE:
      return {
        ...state,
        gstNumber: action.payload,
      };

    case COMPANY_NAME_UPDATE:
      return {
        ...state,
        companyName: action.payload,
      };

    case ADDRESS_UPDATE:
      return {
        ...state,
        companyAddress: action.payload,
      };

    case SAVE_GST_DETAIL:
      return {
        ...state,
        saveGstDetails: action.payload,
      };

    case STATE_ERROR:
      return {
        ...state,
        errorState: {
          ...state.errorState,
          ...action.payload as Partial<Error>
        },
      };

      case COMPANY_NAME_ERROR:
        return {
          ...state,
          errorState: {
            ...state.errorState,
            ...action.payload as Partial<Error>
          },
        };

        case ADDRESS_ERROR: 
        return {
          ...state,
          errorState: {
            ...state.errorState,
            ...action.payload as Partial<Error>
          },
        };

    default:
      return state;
  }
};

export default reducer;
