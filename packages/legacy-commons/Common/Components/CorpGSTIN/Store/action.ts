import { Error } from '../Helpers/interface';

export const INITIALISE_CORP_GSTIN = '@corp_gstin/Initialise';
export const STATE_UPDATE = '@corp_gstin/State Update';
export const GST_NUMBER_UPDATE = '@corp_gstin/GST Number Update';
export const ADDRESS_UPDATE = '@corp_gstin/Address Update';
export const COMPANY_NAME_UPDATE = '@corp_gstin/Company Name Update';
export const SAVE_GST_DETAIL = '@corp_gstin/Save GST Detail';

export const STATE_ERROR = '@corp_gstin/State field error';
export const COMPANY_NAME_ERROR = '@corp_gstin/Company name field error';
export const ADDRESS_ERROR = '@corp_gstin/Address field error';

export const initCorpGSTIN = () => ({
  type: INITIALISE_CORP_GSTIN,
});

export const updateState = (state: string) => (dispatch: any) =>
  dispatch({
    type: STATE_UPDATE,
    payload: state,
  });

export const updateGstNumber = (gstNumber: string) => (dispatch: any) =>
  dispatch({
    type: GST_NUMBER_UPDATE,
    payload: gstNumber,
  });

export const updateAddress = (address: string) => (dispatch: any) =>
  dispatch({
    type: ADDRESS_UPDATE,
    payload: address,
  });

export const updateCompanyName = (companyName: string) => (dispatch: any) =>
  dispatch({
    type: COMPANY_NAME_UPDATE,
    payload: companyName,
  });

export const toggleSaveGstDetail = (shouldSaveGstDetail: boolean) => (dispatch: any) =>
  dispatch({
    type: SAVE_GST_DETAIL,
    payload: shouldSaveGstDetail,
  });

export const stateFieldError = (payload: Partial<Error>) => ({
  type: STATE_ERROR,
  payload,
});

export const comapanyNameFieldError = (payload: Partial<Error>) => ({
  type: COMPANY_NAME_ERROR,
  payload,
});

export const addressFieldError = (payload: Partial<Error>) => ({
  type: ADDRESS_ERROR,
  payload,
});
