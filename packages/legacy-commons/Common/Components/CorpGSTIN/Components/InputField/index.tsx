import React, { FC, Fragment, useEffect, useState } from 'react';
import { Text, TextInput } from 'react-native';
import ErrorField from '../ErrorField';
import { InputFieldProps } from '../../Helpers/interface';

const InputField: FC<InputFieldProps> = ({
  ref,
  value = '',
  label,
  onBlur,
  onChange,
  isError,
  onFocus,
  labelStyles,
  placeholder,
  multiline,
  customStyles,
  errorMessage,
  textAlignVertical = 'auto',
}) => {
  const [inputText, setInputText] = useState('');

  useEffect(() => {
    if (value) {
      setInputText(value);
    }

    return () => {
      setInputText('');
    };
  }, [value]);

  const onChangeText = (val: string) => {
    setInputText(val);
    onChange?.(val);
  };

  return (
    <Fragment>
      <Text style={labelStyles}>{label}</Text>
      <TextInput
        value={inputText}
        ref={ref}
        placeholder={placeholder}
        onChangeText={onChangeText}
        style={customStyles}
        onFocus={onFocus}
        onBlur={onBlur}
        multiline={multiline}
        textAlignVertical={textAlignVertical}
      />
      {isError && inputText === '' && <ErrorField message={errorMessage} />}
    </Fragment>
  );
};

export default InputField;
