import { StyleSheet } from 'react-native';
import { colors, fontSizes, fonts } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  closeArea: {
    height: '30%',
  },
  searchBarAndStateListWrapper: {
    height: '70%',
    paddingHorizontal: 16,
    paddingBottom: 40,
    backgroundColor: colors.white,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
  },
  divider: {
    height: 1,
    backgroundColor: colors.lightGray,
    marginHorizontal: -16,
  },
});

export const searchBarStyles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: colors.roseWhite,
    borderWidth: 1,
    borderColor: colors.tomato,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    marginVertical: 16,
  },
  imageStyle: {
    height: 10,
    width: 10,
    marginHorizontal: 16,
    tintColor: colors.greyText1,
  },
  inputFieldWrapper: {
    width: '80%',
    justifyContent: 'flex-start',
  },
  searchBarPlaceholder: {
    color: colors.red10,
    fontFamily: fonts.regular,
    fontWeight: '700',
    fontSize: 12,
    lineHeight: 16,
    textTransform: 'uppercase',
  },
  inputField: {
    backgroundColor: colors.roseWhite,
    fontSize: fontSizes.md,
    fontWeight: '400',
    padding: 0,
    marginTop: 2,
  },
});

export const stateStyles = StyleSheet.create({
  container: {
    marginVertical: 18,
  },
  nameTextStyles: {
    fontFamily: fonts.regular,
    fontSize: fontSizes.lg,
    lineHeight: 19,
    color: colors.black,
    fontWeight: '400',
  },
});
