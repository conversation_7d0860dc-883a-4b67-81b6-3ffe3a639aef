import React, { FC } from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import { stateStyles } from './styles';

type StateProps = {
  state: string;
  onSelect: (arg: string) => void;
};

const State: FC<StateProps> = ({ state, onSelect }) => {
  const onPress = () => onSelect(state);

  return (
    <TouchableOpacity activeOpacity={0.6} onPress={onPress}>
      <View style={stateStyles.container}>
        <Text style={stateStyles.nameTextStyles}>{state}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default State;
