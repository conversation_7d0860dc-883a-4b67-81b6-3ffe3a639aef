import React, { FC, useRef } from 'react';
import { Text, View, TextInput, Image, TouchableOpacity } from 'react-native';

import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { SearchBarProps } from '../../Helpers/interface';

import { searchBarStyles } from './styles';

const backIcon = require('@mmt/legacy-assets/src/backIosGrey.webp');

const SearchBar: FC<SearchBarProps> = ({
  value,
  onChange,
  onClose,
  placeholder = '',
  textInputPlaceholder = '',
}) => {
  const searchRef = useRef(null);

  const onPressHandler = (e: any) => {
    e.preventDefault();
    onClose();
  };

  return (
    <View style={searchBarStyles.container}>
      <TouchableOpacity onPress={onPressHandler}>
        <Image source={backIcon} style={searchBarStyles.imageStyle} />
      </TouchableOpacity>
      <View style={searchBarStyles.inputFieldWrapper}>
        <Text style={searchBarStyles.searchBarPlaceholder}>{placeholder}</Text>
        <TextInput
          autoFocus={true}
          onChangeText={onChange}
          style={searchBarStyles.inputField}
          ref={searchRef}
          value={value}
          placeholder={textInputPlaceholder}
          placeholderTextColor={colors.purpleyGrey}
        />
      </View>
    </View>
  );
};

export default SearchBar;
