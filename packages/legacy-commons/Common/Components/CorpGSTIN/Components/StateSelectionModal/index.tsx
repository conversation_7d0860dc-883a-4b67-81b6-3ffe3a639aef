import React, { FC, useState } from 'react';
import { ScrollView, View, TouchableOpacity } from 'react-native';
import { SELECT_STATE, StatesData, WHERE_IS_YOUR_COMPANY_LOCATED } from '../../Helpers/constants';
import { StateSelectionProps } from '../../Helpers/interface';
import SearchBar from './SearchBar';
import State from './State';
import { styles } from './styles';

const StateSelection: FC<StateSelectionProps> = ({ closeHandler, selectHandler, stateList }) => {
  const [searchString, setSearchString] = useState('');
  const [states, setStates] = useState(stateList);

  const searchedStatesData = (val: string) => {
    setSearchString(val);

    if (val === '') {
      setStates(stateList);
      return;
    }

    const filteredStates = stateList.filter((state) => {
      const stateName = state?.name?.toLowerCase();
      return stateName?.includes(val?.toLowerCase());
    });

    setStates(filteredStates);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={closeHandler} style={styles.closeArea} />
      <View style={styles.searchBarAndStateListWrapper}>
        <SearchBar
          value={searchString}
          onChange={searchedStatesData}
          onClose={closeHandler}
          placeholder={WHERE_IS_YOUR_COMPANY_LOCATED}
          textInputPlaceholder={SELECT_STATE}
        />
        <View style={styles.divider} />
        <ScrollView scrollEnabled>
          {states?.map(({ id, name }) => (
            <State key={id} state={name} onSelect={selectHandler} />
          ))}
        </ScrollView>
      </View>
    </View>
  );
};

export default StateSelection;
