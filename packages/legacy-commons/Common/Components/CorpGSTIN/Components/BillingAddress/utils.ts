import { FieldName } from '../../Helpers/constants';
import { styles } from './styles';

export const getFieldStyles = (focusTextInput: number) => ({
  [FieldName.COMPANY_NAME]: {
    labelStyles: [
      styles.textStyles,
      focusTextInput === FieldName.COMPANY_NAME && styles.activeFieldLabelStyle,
    ],
    inputStyles: [
      styles.textInputStyles,
      focusTextInput === FieldName.COMPANY_NAME && styles.activeTextField,
    ],
  },
  [FieldName.ADDRESS]: {
    labelStyles: [
      styles.textStyles,
      focusTextInput === FieldName.ADDRESS && styles.activeFieldLabelStyle,
    ],
    inputStyles: [
      styles.textInputStyles,
      styles.addressField,
      focusTextInput === FieldName.ADDRESS && styles.activeTextField,
    ],
  },
});
