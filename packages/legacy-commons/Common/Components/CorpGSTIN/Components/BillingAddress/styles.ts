import { StyleSheet } from 'react-native';
import { colors, fontSizes } from '@mmt/legacy-commons/Styles/globalStyles';

export const styles = StyleSheet.create({
  invoice: {
    marginBottom: 4,
    fontFamily: 'Lato',
    fontSize: fontSizes.lg,
    lineHeight: 20,
    fontWeight: '900',
    color: colors.black,
  },
  claimText: {
    marginBottom: 16,
    fontFamily: 'Lato',
    fontSize: fontSizes.reg,
    lineHeight: 16,
    fontWeight: '700',
    color: colors.green,
    paddingRight: 4,
  },
  imageStyles: {
    height: 9,
    width: 9,
  },
  stateText: {
    fontSize: fontSizes.md,
    fontWeight: '600',
    color: colors.textGrey,
  },
  textStyles: {
    fontFamily: 'Lato',
    fontSize: fontSizes.md,
    lineHeight: 20,
    fontWeight: '600',
    color: colors.textGrey,
    marginBottom: 8,
  },
  textInputStyles: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.lightGray,
    paddingVertical: 11,
    paddingRight: 24,
    paddingLeft: 16,
    borderRadius: 4,
    marginBottom: 12,
    fontWeight: '700',
    color: colors.textGrey,
    fontSize: fontSizes.lg,
  },
  labelStyle: {
    fontSize: fontSizes.reg,
    fontWeight: '400',
    color: colors.textGrey,
  },
  addressField: {
    height: 100,
    color: colors.black,
    paddingTop: 12,
  },
  activeTextField: {
    backgroundColor: colors.seashell,
    borderColor: colors.red10,
  },
  activeFieldLabelStyle: {
    color: colors.red10,
  },
});
