import React, { FC, Fragment, useRef, useState } from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';

import InputField from '../InputField';
import ErrorField from '../ErrorField';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';

import {
  CLAIM_TEXT,
  COMPANY_DETAILS_FOR_INVOICE,
  GST_FIELD_PLACEHOLDER,
  GST_NUMBER_TEXT,
  SELECT_STATE,
  STATE_ERROR_MESSAGE,
  WHERE_IS_YOUR_COMPANY_LOCATED,
} from '../../Helpers/constants';

import { styles } from './styles';

const arrowUp = require('@mmt/legacy-assets/src/arrow-down.webp');

type StateAndGstProps = {
  state: string;
  gst: string;
  stateError: boolean;
  onGstChange: (arg: string) => void;
  onStateChange: (arg: boolean) => void;
};

const StateAndGst: FC<StateAndGstProps> = ({
  state,
  onStateChange,
  stateError,
  gst,
  onGstChange,
}) => {
  const [focusTextInput, setFocusTextInput] = useState(0);
  const gstFielfRef = useRef(null);

  const gstLabelStyles = [styles.textStyles, focusTextInput === 1 && styles.activeFieldLabelStyle];
  const gstFieldStyles = [styles.textInputStyles, focusTextInput === 1 && styles.activeTextField];

  return (
    <Fragment>
      <Text style={styles.invoice}>{COMPANY_DETAILS_FOR_INVOICE}</Text>
      <Text style={styles.claimText}>{CLAIM_TEXT}</Text>
      <View>
        <Text style={styles.textStyles}>{WHERE_IS_YOUR_COMPANY_LOCATED}</Text>
        <TouchableOpacity
          style={[styles.textInputStyles, AtomicCss.paddingRight15]}
          onPress={() => onStateChange(true)}
          activeOpacity={1}
        >
          <Image source={arrowUp} style={styles.imageStyles} />
          <Text style={[styles.stateText, state === '' && { color: colors.grey }]}>
            {state === '' ? SELECT_STATE : state}
          </Text>
        </TouchableOpacity>
        {stateError && state === '' && <ErrorField message={STATE_ERROR_MESSAGE} />}
      </View>
      <View>
        <InputField
          value={gst}
          onBlur={() => setFocusTextInput(0)}
          ref={gstFielfRef}
          placeholder={GST_FIELD_PLACEHOLDER}
          onChange={onGstChange}
          customStyles={gstFieldStyles}
          onFocus={() => setFocusTextInput(1)}
          isError={false}
          errorMessage=""
          label={GST_NUMBER_TEXT}
          labelStyles={gstLabelStyles}
        />
      </View>
    </Fragment>
  );
};

export default StateAndGst;
