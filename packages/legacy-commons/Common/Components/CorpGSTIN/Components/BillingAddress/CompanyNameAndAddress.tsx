import React, { FC, Fragment, useRef, useState } from 'react';
import { View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import Checkbox from '../../../Checkbox';
import InputField from '../InputField';
import { getFieldStyles } from './utils';
import {
  ADDRESS_FIELD_PLACEHOLDER,
  CHECKBOX_LABEL,
  COMPANY_ADDRESS,
  COMPANY_ADDRESS_ERROR_MESSAGE,
  COMPANY_NAME,
  COMPANY_NAME_ERROR_MESSAGE,
  COMPANY_NAME_PLACEHOLDER,
  FieldName,
} from '../../Helpers/constants';

import { styles } from './styles';

type CompanyNameAndAddressProps = {
  comapany: string;
  address: string;
  saveGstDetails: boolean;
  onCompanyChange: (arg: string) => void;
  onAddressChange: (arg: string) => void;
  onSaveDetails: (arg: boolean) => void;
  companyNameError: boolean;
  addressError: boolean;
  showSaveNow: boolean;
};

const CompanyNameAndAddress: FC<CompanyNameAndAddressProps> = ({
  comapany,
  address,
  saveGstDetails,
  onCompanyChange,
  onAddressChange,
  onSaveDetails,
  companyNameError,
  addressError,
  showSaveNow,
}) => {
  const [focusTextInput, setFocusTextInput] = useState(0);

  const companyNameFielfRef = useRef(null);
  const addressFielfRef = useRef(null);

  const fieldStyles = getFieldStyles(focusTextInput);

  const companyLabelStyles = fieldStyles[FieldName.COMPANY_NAME].labelStyles;
  const companyNameFieldStyles = fieldStyles[FieldName.COMPANY_NAME].inputStyles;
  const addressLabelStyles = fieldStyles[FieldName.ADDRESS].labelStyles;
  const addressFieldStyles = fieldStyles[FieldName.ADDRESS].inputStyles;

  const onBlur = () => {
    setFocusTextInput(0);
  };

  return (
    <Fragment>
      <View>
        <InputField
          value={comapany}
          onBlur={onBlur}
          ref={companyNameFielfRef}
          placeholder={COMPANY_NAME_PLACEHOLDER}
          onChange={onCompanyChange}
          customStyles={companyNameFieldStyles}
          onFocus={() => setFocusTextInput(2)}
          label={COMPANY_NAME}
          labelStyles={companyLabelStyles}
          isError={companyNameError}
          errorMessage={COMPANY_NAME_ERROR_MESSAGE}
        />
      </View>
      <View>
        <InputField
          multiline={true}
          value={address}
          onBlur={onBlur}
          ref={addressFielfRef}
          placeholder={ADDRESS_FIELD_PLACEHOLDER}
          onChange={onAddressChange}
          customStyles={addressFieldStyles}
          onFocus={() => setFocusTextInput(3)}
          textAlignVertical="top"
          label={COMPANY_ADDRESS}
          labelStyles={addressLabelStyles}
          isError={addressError}
          errorMessage={COMPANY_ADDRESS_ERROR_MESSAGE}
        />
      </View>
      {showSaveNow && (
        <Checkbox
          label={CHECKBOX_LABEL}
          checked={saveGstDetails}
          tintColor={colors.goldenYellow6}
          onChange={() => onSaveDetails(!saveGstDetails)}
          labelStyle={styles.labelStyle}
          containerStyle={AtomicCss.alignCenter}
        />
      )}
    </Fragment>
  );
};

export default CompanyNameAndAddress;
