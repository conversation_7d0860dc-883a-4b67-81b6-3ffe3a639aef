import React, { FC } from 'react';
import { View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

import CompanyNameAndAddress from './CompanyNameAndAddress';
import StateAndGst from './StateAndGst';

import { BillingAddressProps } from '../../Helpers/interface';
import { DEFAULT_Fn, DEFAULT_STRING } from '../../Helpers/constants';

const BillingAddress: FC<BillingAddressProps> = ({
  state = DEFAULT_STRING,
  gst = DEFAULT_STRING,
  comapany = DEFAULT_STRING,
  address = DEFAULT_STRING,
  saveGstDetails = false,
  onStateChange = DEFAULT_Fn,
  onGstChange = DEFAULT_Fn,
  onCompanyChange = DEFAULT_Fn,
  onAddressChange = DEFAULT_Fn,
  onSaveDetails = DEFAULT_Fn,
  errorState,
}) => (
  <View style={AtomicCss.paddingAll18}>
    <StateAndGst
      state={state}
      gst={gst}
      stateError={errorState.stateError}
      onGstChange={onGstChange}
      onStateChange={onStateChange}
    />
    <CompanyNameAndAddress
      comapany={comapany}
      address={address}
      saveGstDetails={saveGstDetails}
      onCompanyChange={onCompanyChange}
      onAddressChange={onAddressChange}
      onSaveDetails={onSaveDetails}
      companyNameError={errorState.companyNameError}
      addressError={errorState.addressError}
      showSaveNow={Boolean(gst)}
    />
  </View>
);

export default BillingAddress;
