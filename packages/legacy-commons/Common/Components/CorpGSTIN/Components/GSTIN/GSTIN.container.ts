import { connect } from 'react-redux';
import CorpGSTIN from './GSTIN';

import {
  initCorpGSTIN,
  updateState,
  updateGstNumber,
  updateCompanyName,
  updateAddress,
  toggleSaveGstDetail,
  comapanyNameFieldError,
  addressFieldError,
} from '../../Store/action';
import { DispatchProps, StateProps } from '../../Helpers/interface';

const mapStateToProps = ({
  corpGstinReducer: {
    companyAddress,
    companyName,
    state,
    gstNumber,
    saveGstDetails,
    errorState,
  },
}: any) => ({
  state,
  gstNumber,
  companyName,
  companyAddress,
  saveGstDetails,
  errorState,
});

const mapDispatchToProps = (dispatch: any) => ({
  initialise: () => dispatch(initCorpGSTIN()),
  stateUpdate: (state: string) => dispatch(updateState(state)),
  gstNumberUpdate: (gstNumber: string) => dispatch(updateGstNumber(gstNumber)),
  companyNameUpdate: (companyName: string) => dispatch(updateCompanyName(companyName)),
  addressUpdate: (address: string) => dispatch(updateAddress(address)),
  saveGstDetailToggle: (shoudSaveGstDetail: boolean) => dispatch(toggleSaveGstDetail(shoudSaveGstDetail)),
  resetComapanyNameFieldErr: () => dispatch(comapanyNameFieldError({ companyNameError: false })),
  resetAddressFieldErr: () => dispatch(addressFieldError({ addressError: false })),
});

export default connect<StateProps, DispatchProps, {}>(
  mapStateToProps,
  mapDispatchToProps,
)(CorpGSTIN);
