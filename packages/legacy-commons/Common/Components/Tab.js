import React from 'react';
import {Text, TouchableOpacity} from 'react-native';
import PropTypes from 'prop-types';
import {fonts} from '../../Styles/globalStyles';

export default function Tab({
  index, item, activeTab, onHandleTabChange = null
}) {
  const tabStyle = [styles.tabStyle];
  const tabStyleTxt = [styles.tabStyleTxt];

  if (index === activeTab) {
    tabStyle.push(styles.tabActive);
    tabStyleTxt.push(styles.tabTxtActive);
  }

  return (
    <TouchableOpacity style={tabStyle} onPress={() => onHandleTabChange(index)}>
      <Text style={tabStyleTxt}>{item}</Text>
    </TouchableOpacity>
  );
}

Tab.propTypes = {
  index: PropTypes.number.isRequired,
  item: PropTypes.string.isRequired,
  activeTab: PropTypes.number.isRequired,
  onHandleTabChange: PropTypes.func
};

const styles = {
  tabStyle: {
    paddingVertical: 10,
    width: '50%'
  },
  tabStyleTxt: {
    color: '#4a4a4a',
    fontFamily: fonts.regular,
    fontSize: 14,
    textAlign: 'center'
  },
  tabActive: {
    borderBottomColor: '#008cff',
    borderBottomWidth: 3
  },
  tabTxtActive: {
    color: '#000',
    fontFamily: fonts.black
  },
  disabledTxt: {
    color: 'rgba(74,74,74,.3)'
  }
};
