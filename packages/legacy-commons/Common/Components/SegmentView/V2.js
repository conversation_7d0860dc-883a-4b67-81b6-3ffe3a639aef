import React, { Component } from 'react';
import {
  Dimensions,
  Image,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import PropTypes from 'prop-types';
import _ from 'lodash';
import getPlatformElevation from '../Card/getPlatformElevation';
import { colors, fonts, normalisePx } from '../../../Styles/globalStyles';

const segmentCheck = require('@mmt/legacy-assets/src/segment_check.webp');
const CABS_LOB = 'CABS';

class SegmentView extends Component {
  constructor(props) {
    super(props);
    this._styles = createStyle(props);
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.height !== this.props.height || nextProps.padding !== this.props.padding) {
      this._styles = createStyle(nextProps);
    }
  }

  render() {
    const { themeColor = colors.azure, lob = null } = this.props;
    const segmentCount = this.props.segments.length;
    let containerWidthStyle = this._styles.containerWidthSmall;
    let segmentWidthStyle = [{ flexBasis: '50%' }, { flexBasis: '50%' }];
    let selectedSegmentWidthStyle = [{ flexBasis: '50%' }, { flexBasis: '50%' }];
    if (segmentCount > 2) {
      containerWidthStyle = this._styles.containerWidthLarge;
      segmentWidthStyle = [{ flexBasis: '32%' }, { flexBasis: '35%' }, { flexBasis: '33%' }];
      selectedSegmentWidthStyle = [
        { flexBasis: '32%' },
        { flexBasis: '35%' },
        { flexBasis: '33%' },
      ];
    }
    return (
      <View style={[this._styles.segmentContainer, containerWidthStyle]}>
        {this.props.segments.map((segment, i) => {
          let displayName;
          let disabled = false;
          if (_.isObject(segment)) {
            displayName = segment.value;
            disabled = !segment.enabled;
          } else {
            displayName = segment;
          }
          const isActive = this.props.selectedSegmentIndex === i;
          const selectedSegStyle = isActive
            ? { ...this._styles.segmentSelected, backgroundColor: themeColor }
            : {};
          const selectedSegWidthStyle = isActive ? selectedSegmentWidthStyle[i] : {};
          const disabledSegStyle = disabled ? this._styles.segmentDisabled : {};
          const selectedSegContainerStyle = isActive
            ? { ...this._styles.segmentSelectedContainer, backgroundColor: themeColor }
            : {};
          const extraPadding = this.props.showIcon ? {} : this._styles.horizonatalPadding;
          return (
            <TouchableOpacity
              testID={`segment_${segment}`}
              style={[
                this._styles.segment,
                selectedSegContainerStyle,
                extraPadding,
                segmentWidthStyle[i],
                selectedSegWidthStyle,
              ]}
              key={segment}
              feedbackColor={Platform.OS === 'ios' ? 'transparent' : colors.grayBg}
              onPress={() => {
                if (disabled || this.props.selectedSegmentIndex === i) return;
                this.props.onSegmentClicked(i);
              }}
            >
              <View style={{ flexDirection: 'row', flex: 1 }}>
                {isActive && this.props.showIcon && (
                  <Image
                    style={this._styles.checkIcon}
                    source={segmentCheck}
                    resizeMode={'contain'}
                  />
                )}
                <Text style={[this._styles.segmentText, selectedSegStyle, disabledSegStyle]}>
                  {displayName.toUpperCase()}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  }
}

const createStyle = ({ height, padding }) => {
  let windowWidth = Dimensions.get('window').width;
  windowWidth = windowWidth > 100 ? windowWidth : 320;
  const segmentContainerHeight = height;
  const segmentPadding = padding;
  const segmentHeight = segmentContainerHeight - 2 * segmentPadding;
  const segmentOuterRadius = segmentContainerHeight / 10;
  const segmentInnerRadius = segmentHeight / 10;

  return StyleSheet.create({
    segmentContainer: {
      alignSelf: 'center',
      alignItems: 'stretch',
      flexDirection: 'row',
      minHeight: segmentContainerHeight,
      borderRadius: segmentOuterRadius,
      ...getPlatformElevation(Platform.select({ ios: 5, android: 5 })),
      ...Platform.select({
        web: {
          borderColor: colors.lightGrey2,
          borderWidth: 1,
        },
      }),
      padding: segmentPadding,
      backgroundColor: colors.white,
      shadowRadius: 2,
    },
    containerWidthSmall: {
      width: Math.min(250, windowWidth * 0.8),
    },
    containerWidthLarge: {
      width: Math.min(315, windowWidth * 0.95),
    },
    containerWidthExpand: {
      width: Math.max(315, windowWidth * 0.9),
    },
    segmentPadding: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    segmentDividerPadding: {
      paddingRight: segmentPadding,
    },
    horizonatalPadding: {
      paddingHorizontal: normalisePx(8),
    },
    segment: {
      flexBasis: '50%',
      minHeight: segmentHeight,
      paddingVertical: segmentPadding,
      paddingHorizontal: segmentPadding,
      borderRadius: segmentInnerRadius,
      backgroundColor: colors.white,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'center',
    },
    segmentSelectedContainer: {
      backgroundColor: colors.azure,
      alignSelf: 'center',
    },
    segmentSelected: {
      backgroundColor: colors.azure,
      color: colors.white,
      fontSize: 11,
      marginHorizontal: 2,
      fontFamily: fonts.black,
      alignSelf: 'center',
      textAlign: 'center',
      flex: 1,
    },
    segmentDisabled: {
      color: colors.lightestTextColor,
      borderRadius: 0,
      alignSelf: 'center',
      fontFamily: fonts.black,
    },
    segmentText: {
      fontSize: 11,
      backgroundColor: colors.white,
      marginHorizontal: 2,
      color: colors.defaultTextColor,
      fontFamily: fonts.black,
      overflow: 'hidden',
      alignSelf: 'center',
      textAlign: 'center',
      flex: 1,
    },
    checkIcon: {
      alignSelf: 'flex-start',
      height: 19,
      width: 19,
      marginRight: 2,
    },
  });
};

SegmentView.propTypes = {
  height: PropTypes.number,
  padding: PropTypes.number,
  segments: PropTypes.array.isRequired,
  onSegmentClicked: PropTypes.func.isRequired,
  selectedSegmentIndex: PropTypes.number,
  showIcon: PropTypes.bool,
};

SegmentView.defaultProps = {
  selectedSegmentIndex: 0,
  showIcon: true,
  height: 44,
  padding: 6,
};

export default SegmentView;
