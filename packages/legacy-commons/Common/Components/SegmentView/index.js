import React, {Component} from 'react';
import {Image, StyleSheet, Text, View, Platform, PixelRatio} from 'react-native';
import PropTypes from 'prop-types';
import _ from 'lodash';
import getPlatformElevation from '../Card/getPlatformElevation';
import {colors, fonts, normalisePx} from '../../../Styles/globalStyles';
import TouchableRipple from '../TouchableRipple';

const SegmentCheck = require('@mmt/legacy-assets/src/segment_check.webp');

class SegmentView extends Component {
  render() {
    return (
      <View style={styles.segmentContainer}>
        {this.props.segments.map((segment, i) => {
          let displayName;
          let disabled = false;
          if (_.isObject(segment)) {
            displayName = segment.value;
            disabled = !segment.enabled;
          } else {
            displayName = segment;
          }
          const {segmentSelectedStyle, segmentTextStyle, iconStyle, segmentSelectedStyleProp, segmentSelectedContainerStyleProp, segmentCheckIcon} = this.props;
          const isActive = this.props.selectedSegmentIndex === i;
          const selectedSegStyle = isActive ? {...styles.segmentSelected, ...segmentSelectedStyleProp} : {};
          const disabledSegStyle = disabled ? styles.segmentDisabled : {};
          const selectedSegContainerStyle = isActive ? {...styles.segmentSelectedContainer, ...segmentSelectedContainerStyleProp} : {};
          return (
            <TouchableRipple
              testID={`segment_${segment}`}
              key={segment}
              feedbackColor={Platform.OS === 'ios' ? 'transparent' : colors.grayBg}
              onPress={() => {
                if (disabled) return;
                this.props.onSegmentClicked(i);
              }}
            >
              <View style={styles.segmentPadding}>
                <View style={[styles.segment, selectedSegContainerStyle]}>
                  {isActive &&
                    <Image
                      style={[styles.checkIcon, iconStyle]}
                      source={segmentCheckIcon}
                      resizeMode={'contain'}
                    />
                  }
                  <Text style={[styles.segmentText, segmentTextStyle, selectedSegStyle, disabledSegStyle, segmentSelectedStyle]}>
                    {displayName.toUpperCase()}
                  </Text>
                </View>
              </View>
            </TouchableRipple>
          );
        })}
      </View>
    );
  }
}


const styles = StyleSheet.create({
  segmentContainer: {
    alignSelf: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    borderRadius: normalisePx(21),
    ...getPlatformElevation(Platform.select({ios: 1.5, android: 3, web: 3})),
    backgroundColor: colors.white,
    shadowRadius:2

  },
  segmentPadding: {
    paddingVertical: normalisePx(5),
    alignItems: 'center',
    justifyContent: 'center'
  },
  segment: {
    borderRadius: normalisePx(18),
    backgroundColor: colors.white,
    flexDirection: 'row',
    padding: 0,
    marginHorizontal: normalisePx(8),
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center'
  },
  segmentSelectedContainer: {
    padding: normalisePx(2),
    borderRadius: normalisePx(18),
    marginHorizontal: normalisePx(5),
    alignSelf: 'center'
  },
  segmentSelected: {
    padding: normalisePx(4),
    borderRadius: normalisePx(18),
    color: colors.white,
    marginHorizontal: normalisePx(5),
    fontSize: (12),
    fontFamily: fonts.black,
    alignSelf: 'center',
    textAlign: 'center'
  },
  segmentDisabled: {
    color: colors.lightestTextColor,
    borderRadius: 0,
    alignSelf: 'center',
    fontFamily: fonts.black
  },
  segmentText: {
    fontSize: (12),
    backgroundColor: colors.white,
    marginHorizontal: normalisePx(4),
    color: colors.black,
    fontFamily: fonts.black,
    overflow: 'hidden',
    alignSelf: 'center',
    textAlign: 'center'
  },
  checkIcon: {
    height: normalisePx(22),
    width: normalisePx(22),
    marginHorizontal: normalisePx(2)
  }

});

SegmentView.propTypes = {
  segments: PropTypes.array.isRequired,
  onSegmentClicked: PropTypes.func.isRequired,
  selectedSegmentIndex: PropTypes.number,
  segmentSelectedStyle: PropTypes.object,
  segmentTextStyle: PropTypes.object,
  iconStyle: PropTypes.object,
  segmentSelectedStyleProp: PropTypes.object,
  segmentSelectedContainerStyleProp: PropTypes.object,
  segmentCheckIcon: Image.propTypes.source
};

SegmentView.defaultProps = {
  selectedSegmentIndex: 0,
  segmentSelectedStyleProp: {backgroundColor: colors.azure},
  segmentCheckIcon: SegmentCheck,
  segmentSelectedContainerStyleProp: {backgroundColor: colors.azure},
  segmentSelectedStyle: {},
  segmentTextStyle: {},
  iconStyle: {}
};

export default SegmentView;
