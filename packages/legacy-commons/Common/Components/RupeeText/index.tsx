import React from 'react';
import { Text } from 'react-native';
/*
 * A wrapper around the <Text>
 * Issue : Text were being truncated at the end or not visible when switched to Hindi language.
 * Temporary solution : Add a empty space at the end of the string if string contains rupee symbol.
 *
 * Usage
 * Just replace 'react-native' with 'src/react-native' in import statement
 * or
 * import RupeeText from 'src/Common/Components/RupeeText'
 * Replace <Text> with <RupeeText>
 */
const RupeeText = ({ children, ...props }: any) => {
  //TODO: Return as it is if selected language is ENGLISH
  // A/B config required?
  // Should we replace all <Text> occurrence or just the Text with Rs symbol? Rename file accordingly.
  return (
    <Text {...props}>
      {React.Children.map(children, (child) => {
        if (
          typeof child === 'string' &&
          (child.indexOf('\u20B9') > -1 || child.indexOf('₹') > -1)
        ) {
          return `${child} `;
        }
        return child;
      })}
    </Text>
  );
};

export default React.memo(RupeeText);
