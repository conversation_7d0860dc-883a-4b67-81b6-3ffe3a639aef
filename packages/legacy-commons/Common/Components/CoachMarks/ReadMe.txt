*****************************************
*        SAMPLE STEP FOR COACH MARK     *
*****************************************

{
    key: 'unique-value', //Required
    shape: { //Required
      top: 20,
      left: 250,
      right: 0,     //optional
      bottom: 0,    //optional
      type: 'circle', //optional default will be used
      radius: 100 //optional
    },
    arrow: { //Required
      top: 150,
      left: 200,
      right: 0,     //optional
      bottom: 0,    //optional
      type: 'up-right',
    },
    label: { //Required
      top: 232,
      left: 72,
      right: 26,    //optional
      bottom: 298,  //optional
      text: 'These are called plant leaves.',
    },
    cta: { //optional
      direction: 'vertical', //optional
      top: 10,      //optional
      left: 10,     //optional
      right: 0,     //optional
      bottom: 0,    //optional
    }
  }

*****************************************
*        Shape Types                    *
*****************************************

circle, rect , default shape type is circle
NOTE: you can pass 'radius' attribute also if shape type is circle, which will controll its diameter
And in case of a rect you can pass, width, height and borderRadius to draw a rect

*****************************************
*        Arrow Types                    *
*****************************************

left, right, up-left, up-right, down-left, down-right

*****************************************
*        CTA control                    *
*****************************************
if it is present in step object, you can controll layout vertical/horizontal and position in vertical layout
direction : vertical or horizontal, default is horizontal