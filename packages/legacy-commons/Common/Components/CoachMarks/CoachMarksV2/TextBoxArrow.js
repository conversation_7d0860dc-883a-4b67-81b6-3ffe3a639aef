import React from 'react';
import { View, StyleSheet } from 'react-native';

const Arrow = ({ direction }) => {
  const getArrowStyles = () => {
    switch (direction) {
      case 'top':
        return {
          borderTopWidth: 0,
          borderRightWidth: 10,
          borderBottomWidth: 20,
          borderLeftWidth: 10,
          borderTopColor: 'transparent',
          borderRightColor: 'transparent',
          borderBottomColor: '#2A2A2A',
          borderLeftColor: 'transparent',
        };
      case 'left':
        return {
          borderTopWidth: 10,
          borderRightWidth: 20,
          borderBottomWidth: 10,
          borderLeftWidth: 0,
          borderTopColor: 'transparent',
          borderRightColor: '#2A2A2A',
          borderBottomColor: 'transparent',
          borderLeftColor: 'transparent',
        };
      case 'bottom':
        return {
          borderTopWidth: 20,
          borderRightWidth: 10,
          borderBottomWidth: 0,
          borderLeftWidth: 10,
          borderTopColor: '#2A2A2A',
          borderRightColor: 'transparent',
          borderBottomColor: 'transparent',
          borderLeftColor: 'transparent',
        };
      case 'right':
        return {
          borderTopWidth: 10,
          borderRightWidth: 0,
          borderBottomWidth: 10,
          borderLeftWidth: 20,
          borderTopColor: 'transparent',
          borderRightColor: 'transparent',
          borderBottomColor: 'transparent',
          borderLeftColor: '#2A2A2A',
        };
      default:
        return {};
    }
  };

  return (
    <View style={[arrowStyles.arrowContainer, getArrowStyles()]}>
      <View style={arrowStyles.arrow} />
    </View>
  );
};

const arrowStyles = StyleSheet.create({
  arrowContainer: {
    alignItems: 'center',
  },
  arrow: {
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
  },
});

export default Arrow;
