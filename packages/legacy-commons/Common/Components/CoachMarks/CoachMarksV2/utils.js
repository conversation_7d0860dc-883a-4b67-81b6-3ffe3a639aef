import { Dimensions } from 'react-native';

const { width: windowWidth, height: windowHeight } = Dimensions.get('window');

export const getValueBy = (val, widthOrHeight) => {
  if (typeof val === 'string') {
    if (val.endsWith('%')) {
      // if value is given in percentage
      val = widthOrHeight * (parseFloat(val) / 100);
    } else {
      val = parseFloat(val);
    }
  }
  return val;
};

export const getShapeX = ({ shape, from = '' }) => {
  if (shape && shape.left) {
    return getValueBy(shape.left, windowWidth);
  } else if (shape && shape.right) {
    let x = getValueBy(shape.right, windowWidth);
    if (shape.type === 'circle') {
      const r = shape && shape.radius ? shape.radius : radius;
      if (x >= 0) {
        x = windowWidth - (2 * r + x);
      } else {
        x = windowWidth - 2 * r + -1 * x;
      }
    } else if (shape.type === 'rect') {
      const w = shape && shape.width ? getValueBy(shape.width, windowWidth) : width;
      if (x >= 0) {
        x = windowWidth - (w + x);
      } else {
        x = windowWidth - w + x;
      }
    }
    return x;
  }
  return 0;
};

export const getShapeY = ({ shape }) => {
  if (shape && shape.top) {
    return getValueBy(shape.top, windowHeight);
  } else if (shape && shape.bottom) {
    let y = getValueBy(shape.bottom, windowHeight);
    if (shape.type === 'circle') {
      const r = shape && shape.radius ? shape.radius : radius;
      if (y >= 0) {
        y = windowHeight - (2 * r + y);
      } else {
        y = windowHeight - 2 * r + -1 * y;
      }
    } else if (shape.type === 'rect') {
      const h = shape && shape.height ? getValueBy(shape.height, windowHeight) : height;
      if (y >= 0) {
        y = windowHeight - (h + y);
      } else {
        y = windowHeight - h + y;
      }
    }
    return y;
  }
  return 0;
};
