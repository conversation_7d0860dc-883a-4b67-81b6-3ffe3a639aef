*****************************************
*        SAMPLE STEP FOR COACH MARK V2    *
*****************************************

{
    key: 'unique-value', //Required
    shape: { // Required if not using DynamicCoachMark
      top: 20,
      left: 250,
      right: 0,     //optional
      bottom: 0,    //optional
      type: 'circle', //optional default will be used if using DynamicCoachMark otherwise need to mention
      radius: 100 //optional
    },
    arrow: { //Optional if not send we would directly use 50% for rect shape and center position of circle
      left: 200, // only used for position
    },
    label: { //Require -> Position is taken according the position of shape + 30px more
      title: 'Introducing Collections',
      subTitle: 'Easily find revelant packages based on your interest'
    },
  }

*****************************************
*        Shape Types                    *
*****************************************

circle, rect , default shape type is circle
NOTE: you can pass 'radius' attribute also if shape type is circle, which will controll its diameter
And in case of a rect you can pass, width, height and borderRadius to draw a rect