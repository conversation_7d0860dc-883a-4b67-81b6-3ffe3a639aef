import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Dimensions, Text, Image, TouchableOpacity } from 'react-native';
import Svg, { Rect, ClipPath, Defs, Circle } from 'react-native-svg';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';
import {
  getDataFromStorage,
  setDataInStorage,
  KEY_HOL_CUES_STEPS_SHOWN,
  KEY_HOL_ONBOARDING_PAGE_VISIT,
} from '../../../../AppState/LocalStorage';
import { isEmpty } from 'lodash';
import TextBoxArrow from './TextBoxArrow';
import { getShapeX, getShapeY, getValueBy } from './utils';
const { width: windowWidth, height: windowHeight } = Dimensions.get('window');
export const shapeTypes = {
  circle: 'circle',
  rect: 'rect',
};
const defaultShape = shapeTypes.circle;
const backgroundColor = '#000';
const opacity = 0.7;
const width = windowWidth;
const height = 100;
const radius = 70;
const borderRadius = 0;
/**
 * This component can be used to show onboarding coach marks or cues of a screen to describe fetures on a screen,
 * component also saves steps that are already shown to the user in via key 'KEY_HOL_CUES_STEPS_SHOWN', you can use this data to filter your steps if you need.
 * component also saves last visit time agains pageName and can be accessed via key 'KEY_HOL_ONBOARDING_PAGE_VISIT'
 * Component draws a shape (rect/circle) with step numbers, text and SKIP, NEXT CTA.
 * All components i.e. shape, direction arrow and step text can be positioned using some options, please go through the ReadMe.txt for more details
 * ReadMe is having step object keys with required and optional attributes.
 */
const CoachMarksV2 = (props) => {
  const {
    selectedStepIndex = 0,
    trackEvent,
    onStart,
    pageName,
    steps,
    onStepChange,
    onDone,
    onSkip,
  } = props;
  const [stepIndex, setStepIndex] = useState(selectedStepIndex);
  useEffect(() => {
    onLocalStart();
  }, []);

  useEffect(() => {
    onStepChange(getStep());
  }, [stepIndex]);

  const trackLocalEvent = (event) => {
    if (trackEvent) {
      const step = getStep();
      trackEvent(`${event}_${stepIndex + 1}_${step.key}`);
    }
  };
  const onLocalStart = () => {
    saveStep(0);
    if (onStart) {
      onStart(getStep());
    }
  };
  const saveStep = async (index) => {
    let obj = await getDataFromStorage(KEY_HOL_CUES_STEPS_SHOWN);
    if (isEmpty(obj)) {
      obj = {};
    }
    if (!(pageName in obj)) {
      obj[pageName] = {};
    }
    obj[pageName][steps[index].key] = 1;
    await setDataInStorage(KEY_HOL_CUES_STEPS_SHOWN, obj);
  };

  const savePageVisitTime = async () => {
    let obj = await getDataFromStorage(KEY_HOL_ONBOARDING_PAGE_VISIT);
    if (isEmpty(obj)) {
      obj = {};
    }
    obj[pageName] = new Date().getTime();
    await setDataInStorage(KEY_HOL_ONBOARDING_PAGE_VISIT, obj);
  };

  const getStep = (index) => steps[index || stepIndex];

  const isLastStep = () => stepIndex === steps.length - 1;

  const onNext = () => {
    if (!isLastStep()) {
      trackLocalEvent('next_cue');
    }
    if (stepIndex + 1 < steps.length) {
      const localStepIndex = stepIndex + 1;
      saveStep(localStepIndex);
      setStepIndex(localStepIndex);
    } else if (onDone) {
      trackLocalEvent('next_cue_last');
      savePageVisitTime();
      onDone();
    }
  };

  const onLocalSkip = () => {
    trackLocalEvent('skip_cue');
    savePageVisitTime();
    if (onSkip) {
      onSkip();
    }
  };

  const renderSkipCTA = () =>
    isLastStep() ? (
      <View stype={styles.skipCTAContainer} /> // need empty space
    ) : (
      <TouchableOpacity activeOpacity={0.6} onPress={onLocalSkip} style={styles.skipCTAContainer}>
        <Text style={styles.skipCTA}>SKIP</Text>
      </TouchableOpacity>
    );

  const renderNextCTA = () => (
    <TouchableOpacity activeOpacity={0.8} onPress={onNext}>
      <LinearGradient
        style={{ width: 100, borderRadius: 8, marginBottom: 6 }}
        colors={['#53B2FE', '#065AF3']}
        start={{ x: 0.1, y: 0.1 }}
        end={{ x: 1, y: 1.0 }}
      >
        <Text style={styles.nextCTA}>{isLastStep() ? 'GOT IT' : 'NEXT'}</Text>
      </LinearGradient>
    </TouchableOpacity>
  );

  const renderStepCount = () => {
    const totalSteps = steps.length;
    return (
      <View style={styles.stepCountBoxContainer}>
        {steps.map((_, index) => {
          if (stepIndex === index) {
            return (
              <View style={styles.stepCountContainer}>
                <Text style={styles.stepCount}>{`${stepIndex + 1}/${totalSteps} `}</Text>
              </View>
            );
          } else {
            return <View style={styles.stepCountCircle} />;
          }
        })}
      </View>
    );
  };

  const getLabelPositionValues = () => {
    const { label, shape, arrow } = getStep();
    const shapeTop = getValueBy(shape.top, windowHeight);
    const shapeBottom = getValueBy(shape.bottom, windowHeight);
    const shapeHeight =
      shape?.type === shapeTypes.circle
        ? 2 * shape?.radius
        : getValueBy(shape.height, windowHeight);

        return label
      ? {
          ...(shape?.top && { top: shapeTop + (shapeHeight || height) + 30 }),
          ...(shape?.bottom && { bottom: shapeBottom + (shapeHeight || height) + 30 }),
          left: 20,
          width: label?.width || windowWidth - 40,
        }
      : {};
  };

  const getArrowPositionValues = () => {
    const { label, shape, arrow } = getStep();
    const arrowHorizontalPositionValue = arrow.direction === 'bottom' ? { bottom: -15 } : { top: -20 };
    return {
      position: 'absolute',
      ...arrowHorizontalPositionValue,
      left: arrow?.left
        ? arrow.left
        : shape?.type === shapeTypes.circle
        ? getShapeX({ shape })
        : '50%',
    };
  }
  const renderTextBox = () => {
    const { label, shape, arrow } = getStep();
    const labelContainerStyles = getLabelPositionValues();
    const arrowStyles = getArrowPositionValues()

    return (
      <View style={[styles.textContainer, labelContainerStyles]}>
        <View style={arrowStyles}>
          <TextBoxArrow direction={arrow?.direction || 'top'} />
        </View>
        <Text style={styles.title}>{label.title}</Text>
        <Text style={styles.subTitle}>{label.subTitle}</Text>
        <View style={styles.textBoxFooterContainer}>
          {renderSkipCTA()}
          {renderStepCount()}
          {renderNextCTA()}
        </View>
      </View>
    );
  };
  const renderShape = () => {
    const { shape } = getStep();
    const shapeType = shape && (shape.type || defaultShape);

    return (
      <Svg width={windowWidth} height={windowHeight} viewBox={`0 0 ${windowWidth} ${windowHeight}`}>
        <Defs key={Math.random()}>
          <ClipPath id="clip">
            {shapeType === shapeTypes.circle
              ? renderCircle()
              : shapeType === shapeTypes.rect
              ? renderRect()
              : null}
            <Rect x={0} y={0} width={windowWidth} height={windowHeight} />
          </ClipPath>
        </Defs>
        <Rect
          x={0}
          y={0}
          width={windowWidth}
          height={windowHeight}
          fill={backgroundColor}
          clipPath="url(#clip)"
          opacity={opacity}
        >
          <ClipPath />
        </Rect>
      </Svg>
    );
  };

  const renderCircle = () => {
    const { shape } = getStep();
    const x = getShapeX({ shape });
    const y = getShapeY({ shape });
    const r = shape && shape.radius ? shape.radius : radius;
    return <Circle x={x + r} y={y + r} r={r} />;
  };
  const renderRect = () => {
    const { shape } = getStep();
    const x = getShapeX({ shape });
    const y = getShapeY({ shape });
    const w = shape && shape.width ? shape.width : width;
    const h = shape && shape.height ? shape.height : height;
    const r = shape && (shape.borderRadius || borderRadius);
    return <Rect x={x} y={y} width={w} height={h} rx={r} ry={r} />;
  };

  return (
    <View style={styles.container}>
      {renderShape()}
      {renderTextBox()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: 10,
  },
  textContainer: {
    position: 'absolute',
    backgroundColor: '#2A2A2A',
    padding: 20,
    borderRadius: 8,
    flex: 1,
  },
  title: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Lato-Bold',
    fontWeight: '700',
    marginTop: 8,
  },
  subTitle: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Lato-Bold',
    fontWeight: '400',
    marginTop: 8,
  },
  textBoxFooterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
    width: '100%',
    justifyContent: 'space-between',
  },
  stepCountBoxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  stepCountContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 6,
  },
  stepCount: {
    color: '#4A4A4A',
    fontSize: 10,
    fontFamily: 'Lato-Black',
    fontWeight: 'bold',
  },
  stepCountCircle: {
    backgroundColor: '#4A4A4A',
    width: 5,
    height: 5,
    borderRadius: 10,
    marginHorizontal: 6,
  },
  skipCTAContainer: {
    borderWidth: 1,
    borderColor: 'white',
    borderRadius: 8,
  },
  skipCTA: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Lato-Black',
    fontWeight: '900',
    textAlign: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  nextCTA: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Lato-Black',
    fontWeight: '900',
    paddingHorizontal: 24,
    paddingVertical: 12,
    textAlign: 'center',
  },
});

export default CoachMarksV2;
CoachMarksV2.propTypes = {
  /**
   * An array of coach marks steps defines shape, label/text and arrow positions
   * */
  steps: PropTypes.array.isRequired,
  /**
   * If given that coach mark step will be used for rendering the screen otherwise first step
   * */
  selectedStepIndex: PropTypes.number,
  /**
   * This function gets called when user viewed all steps successfully
   * */
  onDone: PropTypes.func.isRequired,
  /**
   * This function gets called when user don't want to see coach marks
   * */
  onSkip: PropTypes.func.isRequired,
  /**
   * This function gets called with current step when user clicks on next cta
   * */
  onStepChange: PropTypes.func,
  /**
   * This function gets called when coachmarks displays its first step
   * */
  onStart: PropTypes.func,
  /**
   * This function gets called when user clicks next, skip or got it CTA
   * */
  trackEvent: PropTypes.func,
  /**
   * This prop used to save last visit time pagewise and you can access page last visit object from key KEY_HOL_ONBOARDING_PAGE_VISIT
   * */
  pageName: PropTypes.string.isRequired,
};
