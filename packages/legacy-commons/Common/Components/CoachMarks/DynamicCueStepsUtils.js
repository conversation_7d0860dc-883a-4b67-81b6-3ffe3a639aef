import { Dimensions } from 'react-native';
import { statusBarHeightForIphone } from '../../../Styles/globalStyles';
import { getValueBy } from './CoachMarksV2/utils';
let cuesStep = {};
const { height: windowHeight } = Dimensions.get('window');
const getCuesStepPositions = () => {
  return cuesStep;
};

const clearCuesStepPositions = () => {
  return (cuesStep = {});
};
const setCueStepPosition = (newCuesStep) => {
  cuesStep = newCuesStep;
};
const resetCueStepPosition = () => (cuesStep = {});

const isCueStepValid = (step, isArrowRequired) => {
  if (!step?.shape && !step?.arrow && !step?.label) {
    return false;
  }

  const { type: shapeType = '' } = step?.shape || {};
  const { type: arrowType = '' } = step?.arrow || {};
  if (!shapeType) {
    return false;
  }

  if (!arrowType && isArrowRequired) {
    return false;
  }
  return true;
};

const getNewCueStep = (step) => {
  const newStep = getCuesStepPositions()[step.key] || null;
  const updatedStep = { ...newStep, ...step };
  if (!isCueStepValid(updatedStep)) {
    return null;
  } else {
    return updatedStep;
  }
};

const getValidCuesSteps = (steps = [], { isArrowRequired = true } = {}) => {
  const validCuesStep = [];
  if (steps.length <= 0) {
    return [];
  }
  steps?.map((step) => {
    if (!isCueStepValid(step, isArrowRequired)) {
      const cueStep = getNewCueStep(step);
      if (cueStep) {
        const updatedCueStep = {
          ...cueStep,
          shape: {
            ...cueStep.shape,
            ...(cueStep.shape.top && { top: getValueBy(cueStep.shape.top, windowHeight) - statusBarHeightForIphone }),
            ...(cueStep.shape.bottom && {
              bottom: getValueBy(cueStep.shape.bottom, windowHeight) + statusBarHeightForIphone,
            }),
          },
        };
        validCuesStep.push(updatedCueStep);
      }
    } else {
      const updatedCueStep = {
        ...step,
        shape: {
          ...step.shape,
          ...(step.shape.top && { top: getValueBy(step.shape.top, windowHeight) - statusBarHeightForIphone }),
          ...(step.shape.bottom && { bottom: getValueBy(step.shape.bottom, windowHeight) + statusBarHeightForIphone }),
        },
      };
      validCuesStep.push(updatedCueStep);
    }
  });
  return validCuesStep;
};

const updatedCuesSteps = ({ updatedCuesSteps = {}, steps = [] }) => {
  // restructure steps to create object of objects
  const formattedSteps = {};
  steps.map((step) => {
    formattedSteps[step.key] = step;
  });

  // updated cue step with new data present
  const upgradedCuesSteps = Object.keys(formattedSteps).map((stepKey) => {
    const { shape = {}, arrow = {}, label = {}, cta = {}, key = '', extraInfo = {} } =
      formattedSteps[stepKey] || {};
    const {
      shape: updatedShape = {},
      label: udpatedLabel = {},
      arrow: updatedArrow = {},
      cta: updatedCTA = {},
      key: updatedKey = '',
      extraInfo: updatedExtraInfo = {},
    } = updatedCuesSteps[stepKey] || {};
    const upgradedStep = {
      shape: {
        ...shape,
        ...updatedShape,
      },
      label: {
        ...label,
        ...udpatedLabel,
      },
      arrow: {
        ...arrow,
        ...updatedArrow,
      },
      cta: {
        ...cta,
        ...updatedCTA,
      },
      extraInfo: {
        ...extraInfo,
        ...updatedExtraInfo,
      },
      key: updatedKey ? updatedKey : key,
    };
    return upgradedStep;
  });
  return upgradedCuesSteps;
};
export {
  getCuesStepPositions,
  setCueStepPosition,
  resetCueStepPosition,
  getNewCueStep,
  isCueStepValid,
  getValidCuesSteps,
  updatedCuesSteps,
  clearCuesStepPositions,
};
