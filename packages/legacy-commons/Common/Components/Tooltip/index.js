import React from 'react';
import {Dimensions, TouchableWithoutFeedback, Image, StyleSheet, View, Platform} from 'react-native';
import PropTypes from 'prop-types';
import _ from 'lodash';
import getPlatformElevation from '../Card/getPlatformElevation';
import TouchableRipple from '../TouchableRipple';
import {colors, normalisePx} from '../../../Styles/globalStyles';
// eslint-disable-next-line import/no-unresolved
const closeIcon = require('@mmt/legacy-assets/src/ic_close_light_green.webp');

class Tooltip extends React.Component {
  _onContentLayout = ({nativeEvent}) => {
    const {
      x, y, height, width
    } = nativeEvent.layout;
    this._bounds = {
      top: y, left: x, bottom: y + height, right: x + width
    };
  };

  _onPress = ({nativeEvent}) => {
    const {pageX, pageY} = nativeEvent;
    const {
      top, left, bottom, right
    } = this._bounds;
    const inBounds = _.inRange(pageY, top, bottom) && _.inRange(pageX, left, right);
    if (!inBounds) {
      this.props.dismiss();
    }
  };

  render() {
    const {layout} = this.props;
    if (_.isNil(layout)) {
      return null;
    }
    const isBottom = this.props.orientation === 'bottom';
    const offset = Platform.OS === 'ios' ? 24 : 0;
    let position = {left: layout.x};
    if (!isBottom) {
      position = {...position, bottom: (Dimensions.get('window').height - layout.y)};
    } else {
      position = {...position, top: (layout.y - offset)};
    }
    return (
      <TouchableWithoutFeedback onPress={this._onPress}>
        <View style={styles.rootContainer} pointerEvents="box-none">
          <View
            style={{
              position: 'absolute',
              ...position,
              ...getPlatformElevation(Platform.select({ios: 6, android: 12}))
            }}
            onLayout={this._onContentLayout}
          >
            {this.props.orientation === 'bottom' &&
            <View style={styles.arrowTop} />
            }
            <View style={styles.innerContainer}>
              <View style={styles.content}>
                {this.props.children}
              </View>
              <View style={styles.closeContainer}>
                <TouchableRipple
                  onPress={this.props.dismiss}
                  feedbackColor={colors.grayBgTransparent}
                >
                  <Image style={styles.close} source={closeIcon} />
                </TouchableRipple>
              </View>
            </View>
            {this.props.orientation === 'top' &&
            <View style={styles.arrowBottom} />
            }
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  }
}


const styles = StyleSheet.create({
  rootContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'transparent'
  },
  innerContainer: {
    backgroundColor: '#26b5a9',
    flexDirection: 'row',
    borderRadius: 2,
    borderColor: colors.transparent,
    padding: 4
  },
  arrowTop: {
    width: 0,
    height: 0,
    marginLeft: normalisePx(16),
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: normalisePx(10),
    borderRightWidth: normalisePx(10),
    borderBottomWidth: normalisePx(8),
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#26b5a9'
  },
  arrowBottom: {
    width: 0,
    height: 0,
    marginLeft: normalisePx(16),
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: normalisePx(10),
    borderRightWidth: normalisePx(10),
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: '#26b5a9'
  },
  content: {
    padding: normalisePx(8)
  },
  closeContainer: {
    alignSelf: 'flex-start'
  },
  close: {
    width: normalisePx(12),
    height: normalisePx(12),
    marginRight: normalisePx(4),
    marginTop: normalisePx(4),
    resizeMode: 'contain',
    alignSelf: 'flex-start'
  }

});

Tooltip.propTypes = {
  children: PropTypes.node.isRequired,
  dismiss: PropTypes.func.isRequired,
  layout: PropTypes.shape({
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired
  }),
  orientation: PropTypes.oneOf(['top', 'bottom'])
};
Tooltip.defaultProps = {
  layout: null,
  orientation: 'top'
};

export default Tooltip;
