import React from 'react';
import {View, StyleSheet} from 'react-native';
import PropTypes from 'prop-types';

const Separator = ({type}) => {
  const separatorStyle = [styles.separator];
  if (type) {
    separatorStyle.push(styles[type]);
  }

  return (
    <View style={separatorStyle}></View>
  );
};

Separator.propTypes = {
  type: PropTypes.string
};

const styles = StyleSheet.create({
  separator: {
    opacity: 0.2,
    borderWidth: 0.5,
    borderColor: '#979797',
    marginVertical: 15
  },
  full: {
    marginHorizontal: -15
  },
  noMargin: {
    marginVertical: 0
  }
});

export default Separator;
