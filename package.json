{"name": "src", "version": "0.0.1", "private": true, "scripts": {"test": "jest", "bundle": "rm -rf web/dist; NODE_ENV=production webpack -p --progress --config web/config/webpack.config.js", "rn_clone": "cd Mobile-mmt-react-native && git checkout $RN_BRANCH && cd ..", "package": "npm run rn_clone && yarn install && npm run bundle && node package_tar.js", "package_dev": "yarn install && npm run bundle && node package_tar.js", "verify": "npm run rn_clone && yarn install && npm run bundle", "start": "NODE_ENV=development webpack-dev-server -d --host 0.0.0.0 --port 3000 --config web/config/webpack.config.js --inline --hot --colors --devtool source-map"}, "rnpm": {"assets": ["./assets/fonts/"]}, "dependencies": {"@babel/core": "^7.0.0", "@react-native-async-storage/async-storage": "1.17.10", "@react-native-picker/picker": "2.5.1", "@react-navigation/native": "^5.9.8", "@react-navigation/stack": "5.2.10", "MMT-UI": "6.3.66", "PAY-UI": "1.1.14", "@Frontend_Ui_Lib_App/BottomSheet": "0.0.6", "@Frontend_Ui_Lib_App/CalendarPicker": "0.0.2", "@Frontend_Ui_Lib_App/Dropdown": "0.0.4", "@Frontend_Ui_Lib_App/RadioButton": "0.0.3", "@Frontend_Ui_Lib_App/SnackBar": "0.0.3", "@Frontend_Ui_Lib_App/Spinner": "^0.0.2", "@Frontend_Ui_Lib_App/SwitchButton": "^0.0.3", "@Frontend_Ui_Lib_App/Tooltip": "0.0.2", "@Frontend_Ui_Lib_App/Accordion": "0.0.2", "@Frontend_Ui_Lib_App/BottomBar": "0.0.1", "@Frontend_Ui_Lib_App/BottomBarFilter": "0.0.1", "@Frontend_Ui_Lib_App/BottomTabs": "0.0.1", "@Frontend_Ui_Lib_App/Counter": "0.0.1", "@Frontend_Ui_Lib_App/Tabs": "0.0.1", "@Frontend_Ui_Lib_App/Button": "0.0.4", "@Frontend_Ui_Lib_App/Carousel": "^0.0.2", "@Frontend_Ui_Lib_App/CheckBox": "0.0.2", "@Frontend_Ui_Lib_App/DateTimeFooter": "0.0.4", "@Frontend_Ui_Lib_App/FloatingInput": "0.0.6", "@Frontend_Ui_Lib_App/DrumRollCalendar": "^0.0.1", "@Frontend_Ui_Lib_App/Header": "0.0.1", "@Frontend_Ui_Lib_App/HighlightedText": "^0.0.1", "@Frontend_Ui_Lib_App/HorizontalDatePicker": "^0.0.3", "@Frontend_Ui_Lib_App/InputField": "0.0.4", "@Frontend_Ui_Lib_App/LineLoader": "0.0.1", "@Frontend_Ui_Lib_App/Persuasion": "0.0.2", "@Frontend_Ui_Lib_App/ProgressBar": "^0.0.1", "@Frontend_Ui_Lib_App/SearchBar": "0.0.3", "@Frontend_Ui_Lib_App/TabSectionScroller": "^0.0.3", "@Frontend_Ui_Lib_App/Shimmer": "0.0.1", "@Frontend_Ui_Lib_App/SlideTabPages": "0.0.2", "@RN_UI_Lib/CalendarCommon": "3.0.0", "react-native-blob-util": "0.19.4", "react-native-tab-view": "^3.5.2", "ad-react-wrapper": "2.5.1", "babel-plugin-transform-remove-console": "^6.9.0", "base-64": "^0.1.0", "base64-js": "^1.3.0", "fecha": "^2.3.2", "history": "^4.7.2", "lodash": "^4.17.15", "memoize-one": "^6.0.0", "modal-enhanced-react-native-web": "^0.2.0", "nuka-carousel": "^4.7.8", "prop-types": "latest", "pwa-mmt-ui-header": "3.3.37", "query-string": "^6.2.0", "raven-js": "^3.27.0", "rc-slider": "^9.7.2", "react": "18.2.0", "react-cookie": "^2.2", "react-cookies": "^0.1.0", "react-dom": "^18.3.1", "react-loadable": "^5.5.0", "react-mobile-datepicker": "^4.0.0", "react-native-ad-wrapper": "1.0.15", "react-native-calendars": "1.1275.0", "react-native-carousel-view": "^0.5.1", "react-native-checkbox": "^2.0.0", "react-native-code-push": "^5.3.2", "react-native-date-picker": "4.2.13", "react-native-dotenv": "^2.4.3", "react-native-easy-rating": "^0.1.3", "react-native-fast-image": "8.5.2", "react-native-file-picker": "0.0.8", "react-native-firebase": "3.0.6", "react-native-gesture-handler": "2.17.1", "react-native-google-analytics": "1.3.2", "react-native-keyboard-aware-scroll-view": "^0.9.4", "react-native-keyboard-aware-view": "0.0.14", "react-native-keyboard-spacer": "0.4.1", "react-native-loading-placeholder": "0.0.6", "react-native-masonry-list": "^2.16.1", "react-native-material-textfield": "^0.12.0", "react-native-md5": "^1.0.0", "react-native-orientation": "3.1.3", "react-native-progress-circle": "^2.0.0", "react-native-qrcode": "^0.2.7", "react-native-reanimated": "2.6.0", "react-native-reversed-flat-list": "^1.0.2", "react-native-safe-area-context": "3.3.2", "react-native-screens": "^3.32.0", "react-native-sentry": "^0.43.2", "react-native-svg": "12.1.1", "react-native-svg-web": "^1.0.9", "react-native-vector-icons": "^4.6.0", "react-native-web": "^0.19.12", "react-native-web-linear-gradient": "^1.1.2", "react-native-web-lottie": "^1.4.3", "react-native-web-webview": "^0.2.8", "react-native-webview": "6.8.0", "react-player": "^1.11.1", "react-redux": "^5.0.6", "react-redux-firebase": "^2.0.0-beta.8", "react-router": "^4.3.1", "react-router-dom": "^4.3.1", "react-router-modal": "^1.5.2", "react-ticker": "^1.2.2", "react-toastify": "^7.0.4", "recyclerlistview": "^1.4.0-beta.5", "redux": "^3.7.2", "redux-act": "^1.7.3", "redux-location-state": "^2.1.0", "redux-logger": "^3.0.6", "redux-persist": "^5.10.0", "redux-thunk": "^2.2.0", "reselect": "^3.0.1", "rn-fetch-blob": "0.12.0", "rn-viewpager": "^1.2.9", "script-ext-html-webpack-plugin": "^2.1.3", "tcomb-form-native": "^0.6.11", "unset-value": "^1.0.0", "url": "^0.11.0", "video-react": "^0.14.0", "zod": "^3.23.8", "mobile-holidays-react-native": "25.9.1-pwa-patch", "@travelplex/floating-icon-web-pwa": "0.0.4"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-typescript": "^7.24.7", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-do-expressions": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-bind": "^7.0.0", "@babel/plugin-proposal-function-sent": "^7.0.0", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-logical-assignment-operators": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-optional-chaining": "^7.0.0", "@babel/plugin-proposal-pipeline-operator": "^7.0.0", "@babel/plugin-proposal-throw-expressions": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-import-meta": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-flow": "^7.0.0", "@babel/runtime": "^7.1.5", "@types/react": "^19.0.1", "@types/lodash": "4.17.0", "@types/react-native": "0.65.1", "autoprefixer": "^9.6.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^9.0.0", "babel-jest": "^24.9.0", "babel-loader": "^8.0.0", "babel-plugin-dynamic-import-webpack": "^1.1.0", "babel-plugin-lodash": "^3.2.11", "babel-plugin-module-resolver": "^3.0.0", "babel-plugin-react-native-web": "^0.9.1", "babel-preset-react": "^6.24.1", "babel-preset-react-native": "~5.0.2", "brotli-webpack-plugin": "^1.1.0", "compression-webpack-plugin": "^0.4.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^3.0.0", "eslint": "^4.16.0", "eslint-config-airbnb": "^16.1.0", "eslint-import-resolver-reactnative": "^1.0.2", "eslint-plugin-import": "^2.8.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-prefer-object-spread": "^1.2.1", "eslint-plugin-react": "^7.7.0", "file-loader": "^2.0.0", "flow-bin": "^0.61.0", "html-webpack-plugin": "^4.0.0-beta.4", "http-serve": "^1.0.1", "jest": "^24.9.0", "jest-fetch-mock": "^1.5.0", "json-loader": "^0.5.4", "lodash-webpack-plugin": "^0.11.5", "metro-react-native-babel-preset": "^0.77.0", "sass": "^1.92.0", "nodejs-fs-utils": "^1.1.0", "offline-plugin": "^5.0.7", "postcss-loader": "^3.0.0", "react-hot-loader": "^1.3.1", "react-native-modal-dropdown": "^1.0.0", "react-native-web-image-loader": "^0.0.6", "react-test-renderer": "16.0.0", "redux-mock-store": "^1.5.1", "sass-loader": "^7.1.0", "style-loader": "^0.23.1", "tar": "^4.4.1", "toposort": "^1.0.3", "typescript": "^3.8.3", "url-loader": "^2.1.0", "webpack": "^4.26.0", "webpack-bundle-analyzer": "^3.4.1", "webpack-cli": "^3.1.2", "webpack-dev-server": "^3.8.0", "webpack-pwa-manifest": "^3.7.1", "webpack-visualizer-plugin": "^0.1.11", "workbox-webpack-plugin": "^3.6.3", "mobile-react-native-styles": "1.0.2", "@travelplex/react-native": "0.2.7", "@ptomasroos/react-native-multi-slider": "2.2.2"}, "jest": {"preset": "react-native", "transform": {"^.+\\.js": "babel-jest"}, "transformIgnorePatterns": ["node_modules/(?!(react-native|react-navigation|mobx-react)/)"], "collectCoverage": true, "collectCoverageFrom": ["!node_modules/**", "!lib/**", "src/**"], "moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/assetsTransformer.js", "\\.(css|less)$": "<rootDir>/assetsTransformer.js", "react-native": "<rootDir>/../"}, "setupFiles": ["./setupJest.js"]}}