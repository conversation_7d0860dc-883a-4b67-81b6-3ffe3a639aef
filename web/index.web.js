import React from 'react';
import {AppRegistry, Image, StyleSheet, View} from 'react-native';
import cookie from 'react-cookies';
import <PERSON> from 'raven-js';
import URL from 'url';
import './WebModules';
import WebRouter from './WebRouter';
import Indigo from './components/images/indigo.png'
import Goibigo from './components/images/goibigo.png'
import Toast from "../Mobile-mmt-react-native/packages/legacy-commons/Common/Components/Toast2";
import GdprConsentForm from 'MMT-UI/GdprConsentForm'
const Aix = 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/AIX-logo.png'

import {
  isGIdisplayPoweredbyMMTHeader,
  updateDeviceType,
  isNonHeaderAffiliate,
} from "mobile-holidays-react-native/src/utils/HolidayUtils";
import {AFFILIATES} from "mobile-holidays-react-native/src/HolidayConstants";
import PoweredByMMT from 'mobile-holidays-react-native/src/Common/Components/PoweredByMMT';
import { handleLocalStorageInGdpr } from 'MMT-UI/storages/client/localStorage'
import { isGdprRegion } from 'MMT-UI/storages/server/isGdprRegion';


class App extends React.Component {
  async getDeviceType(query) {
    let device=(query?.device_type) ? (query?.device_type) :(query?.flavour  ? query?.flavour :null);
    let deviceType = await updateDeviceType(device);
    if(deviceType === "ios" || deviceType === "android" || deviceType === "web") {
      this.setState({deviceType: deviceType});
    }
  }
  constructor(props) {
    super();
    this.state = {
      deviceType:null,
    };
    let aff = AFFILIATES.MMT;
    if(window.location.href.includes("6eholidays.makemytrip.com") || window.location.href.includes("aff=IGO")) {
      aff = AFFILIATES.INDIGO;
    }
    else if(window.location.href.includes("giholidays.makemytrip.com") || window.location.href.includes("aff=GI")){
      aff=AFFILIATES.GI
    }
    else if(window.location.href.includes("hdfcsmartbuyholidays.makemytrip.com") || window.location.href.includes("aff=HDFC_SB")){
      aff=AFFILIATES.HDFC
    }
    else if(window.location.href.includes("airindiaexpressholidays.makemytrip.com") || window.location.href.includes("aff=AFFCGOBIZMMTAIX")){
      aff=AFFILIATES.AIX
    }
    // MMT8421 - Below code was commented to resolve multiple instance issue on holiday Landing page and GI funnel is not open.
    if(aff !== AFFILIATES.MMT) {
      const urlObj = URL.parse(window.location.href, window.location.search);
      const {query} = urlObj;
      this.getDeviceType(query);
    }
    if(!(window.location.href.includes("6eholidays.makemytrip.com") || window.location.href.includes("giholidays.makemytrip.com") || window.location.href.includes("hdfcsmartbuyholidays.makemytrip.com") ||window.location.href.includes("airindiaexpressholidays.makemytrip.com"))){
      handleLocalStorageInGdpr()
    }
  }

  getIndigoBanner=(aff)=>{
    return <View style={[styles.indigoStrip, aff === AFFILIATES.GI ? {justifyContent:'center'}:[]]}>
      { aff === AFFILIATES.GI && <Image source={Goibigo} style={{height:38,width:75}}/>}
      { aff === AFFILIATES.INDIGO && <Image source={Indigo} style={{height:12,width:60}}/>}
      { aff === AFFILIATES.AIX && <Image source={Aix} style={{height:16,width:54,resizeMode:'contain',marginLeft:8}}/>}
      { aff != AFFILIATES.GI ? <PoweredByMMT/> : []}
    </View>
  }
  render() {
    let aff = AFFILIATES.MMT;
    if(window.location.href.includes("6eholidays.makemytrip.com") || window.location.href.includes("aff=IGO")) {
      aff = AFFILIATES.INDIGO;
    }
    else if(window.location.href.includes("giholidays.makemytrip.com") || window.location.href.includes("aff=GI")){
      aff=AFFILIATES.GI
    }
    else if(window.location.href.includes("hdfcsmartbuyholidays.makemytrip.com") || window.location.href.includes("aff=HDFC_SB")){
      aff=AFFILIATES.HDFC
    }
    else if(window.location.href.includes("airindiaexpressholidays.makemytrip.com") || window.location.href.includes("aff=AFFCGOBIZMMTAIX")){
      aff=AFFILIATES.AIX
    }
    return (
        <View style={{flex: 1}}>
          {/* Add checks for affiliate here */}
          {isGIdisplayPoweredbyMMTHeader(aff, this.state?.deviceType) && this.getIndigoBanner(aff)}
          <WebRouter/>
          <Toast/>
          <GdprConsentForm isPWA={true} lob={'HOLIDAYS'}/>
        </View>
    );
  }
}
const styles= StyleSheet.create({
  indigoStrip:{
    height: 35,
    paddingHorizontal:15,
    display:'flex',
    flexDirection:'row',
    alignItems:'center',
    justifyContent:'space-between'
  }
})

AppRegistry.registerComponent('MmtReactNative', () => App);


const rootElement = window.document.getElementById('react-root');
rootElement.innerHTML = '';

AppRegistry.runApplication('MmtReactNative', {
  rootTag: rootElement
});

if (window) {
  if ('serviceWorker' in window.navigator) {
    window.addEventListener('load', () => {
      window.navigator.serviceWorker.register('//jsak.mmtcdn.com/holidays/rn/service-worker.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    });
  }


  window.addEventListener('pageshow', (event) => {
    const historyTraversal = event.persisted ||
      (typeof window.performance !== 'undefined' &&
        window.performance.navigation.type === 2);
    if (historyTraversal) {
      // Handle page restore.
      window.location.reload();
    }
  });
}

const environment = cookie.load('pdt-environment');
if (environment === 'prod') {
  try {
    Raven.config('https://<EMAIL>/149').install();
  } catch (e) {
    console.error('error while loading raven', e);
  }
}

function landscapeLock() {
  const winW = window.innerWidth;
  const landscapeOnlyEl = window.document.getElementById('landscape_screen');
  if (winW > 480) {
    rootElement.style.display = 'none';
    landscapeOnlyEl.style.display = 'flex';
  } else {
    rootElement.style.display = 'flex';
    landscapeOnlyEl.style.display = 'none';
  }
};
window.onresize = landscapeLock;
landscapeLock()

